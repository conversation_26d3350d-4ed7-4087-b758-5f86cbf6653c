# Mail Backup System

A Laravel-based email backup system that can connect to various email protocols (IMAP, POP3, and Exchange Web Services) to fetch and store emails in a database.

## 🚀 Recent Improvements

**Enhanced Message Processing (Latest Update)**
- ✅ **Smart Date Filtering**: Only processes new messages since last check (60-90% performance improvement)
- ✅ **All Folders Support**: Automatically discovers and processes all folders (IMAP/EWS)
- ✅ **Protocol Optimization**: Tailored implementations for each protocol's capabilities
- ✅ **Comprehensive Testing**: Full test suite with 7 tests and 29 assertions
- ✅ **Enhanced API Response**: Detailed folder-by-folder processing results

## Features

- Support for multiple email protocols:
  - **IMAP** - Full folder support with server-side date filtering
  - **POP3** - INBOX only with client-side date filtering
  - **Exchange Web Services (EWS)** - Full folder support with server-side date filtering
- **Smart Message Processing**:
  - Date-based filtering (only new messages since last check)
  - All folder support (IMAP/EWS) or INBOX only (POP3)
  - Duplicate prevention and efficient processing
  - Automatic folder discovery and processing
- **Account Management**:
  - Multiple account support
  - Connection testing and validation
  - Automatic credential optimization for EWS
- **Email Storage**:
  - Complete email metadata storage
  - EML file backup to S3 storage
  - Folder-based organization
  - Attachment handling
- **RESTful API** for integration
- **Comprehensive Testing** with PHPUnit

## Components

### Models

- **Account**: Stores email account credentials and connection settings
- **Email**: Stores email messages fetched from accounts

### Services

- **EmailClient**: A service that handles connections to different email protocols and provides methods for testing connections and fetching emails

### API Endpoints

#### Accounts

- `GET /api/v1/accounts` - List all accounts
- `POST /api/v1/accounts` - Create a new account
- `GET /api/v1/accounts/{id}` - Get account details
- `PUT /api/v1/accounts/{id}` - Update an account
- `DELETE /api/v1/accounts/{id}` - Delete an account
- `GET /api/v1/accounts/{id}/test` - Test the connection to an account
- `GET /api/v1/accounts/{id}/check` - Check for new messages in an account

#### Emails

- `GET /api/v1/mails` - List emails (with filtering options)
- `GET /api/v1/mails/{id}` - Get email details
- `PUT /api/v1/mails/{id}` - Update email properties (read status, flagged status, folder)
- `DELETE /api/v1/mails/{id}` - Delete an email

## Setup

### Prerequisites

- PHP 8.2 or higher
- Composer
- Laravel 12
- Postgre SQL

### Installation

#### Using Laravel Sail (Docker)

1. Clone the repository
2. Set up your environment file:
   ```
   cp .env.example .env
   ```
3. Configure your database in the `.env` file
4. Start Sail:
   ```
   ./vendor/bin/sail up -d
   ```
5. Install dependencies:
   ```
   ./vendor/bin/sail composer install
   ```
6. Generate application key:
   ```
   ./vendor/bin/sail artisan key:generate
   ```
7. Run migrations:
   ```
   ./vendor/bin/sail artisan migrate
   ```

#### Standard Installation

1. Clone the repository
2. Install dependencies:
   ```
   composer install
   ```
3. Set up your environment file:
   ```
   cp .env.example .env
   ```
4. Configure your database in the `.env` file
5. Generate application key:
   ```
   php artisan key:generate
   ```
6. Run migrations:
   ```
   php artisan migrate
   ```
7. Start the server:
   ```
   php artisan serve
   ```

## Creating an Email Account

To create a new email account, send a POST request to `/api/v1/accounts` with the following parameters:

### Common Parameters (Required for all protocols)

- `name`: A friendly name for the account
- `email`: The email address
- `protocol`: One of: `imap`, `pop3`, or `ews`
- `server`: The server hostname
- `port`: The server port
- `username`: The username for authentication
- `password`: The password for authentication
- `use_ssl`: Whether to use SSL (default: true)

### Protocol-Specific Parameters

#### IMAP and POP3

- `folder`: The folder to fetch emails from (default: INBOX) - Only applicable for IMAP

#### Exchange Web Services (EWS)

- `domain`: The domain for authentication (optional)
- `version`: The Exchange version (optional)

### Example Request (IMAP)

```json
{
  "name": "Work Email",
  "email": "<EMAIL>",
  "protocol": "imap",
  "server": "imap.example.com",
  "port": 993,
  "username": "<EMAIL>",
  "password": "your-password",
  "use_ssl": true,
  "folder": "INBOX"
}
```

## Testing Connection

To test if an account can connect successfully:

```
GET /api/v1/accounts/{account_id}/test
```

## Checking for New Messages

To check for new messages and store them in the database:

```
GET /api/v1/accounts/{account_id}/check
```

### Enhanced Message Processing

The system now implements intelligent message processing with the following features:

#### Date-Based Filtering
- **First Run**: Processes all messages in all folders
- **Subsequent Runs**: Only processes messages newer than `last_checked_at` timestamp
- **Efficiency**: Significantly reduces processing time and server load

#### Folder Support
- **IMAP**: Automatically discovers and processes all folders
- **EWS**: Automatically discovers and processes all folders with deep traversal
- **POP3**: Processes INBOX only (protocol limitation)

#### Response Format
```json
{
    "success": true,
    "message": "Found 5 new messages out of 10 total across 3 folders",
    "new_messages": 5,
    "folder_results": {
        "INBOX": {
            "total_messages": 5,
            "new_messages": 3
        },
        "Sent": {
            "total_messages": 3,
            "new_messages": 1
        },
        "Drafts": {
            "total_messages": 2,
            "new_messages": 1
        }
    }
}
```

## Protocol Comparison

| Feature | IMAP | EWS | POP3 |
|---------|------|-----|------|
| **Folder Support** | ✅ All folders | ✅ All folders | ❌ INBOX only |
| **Server-side Date Filter** | ✅ SINCE criteria | ✅ Restriction queries | ❌ Client-side only |
| **Folder Discovery** | ✅ getMailboxes() | ✅ FindFolder API | ❌ N/A |
| **Efficiency** | ⭐⭐⭐ High | ⭐⭐⭐ High | ⭐⭐ Medium |
| **Message Metadata** | ✅ Full support | ✅ Full support | ✅ Full support |
| **Attachment Support** | ✅ Yes | ✅ Yes | ✅ Yes |
| **Read Status** | ✅ Yes | ✅ Yes | ✅ Yes |
| **Typical Use Case** | General email | Enterprise/Office 365 | Simple backup |

### Protocol-Specific Implementation Details

#### IMAP
- Uses `SINCE DD-MMM-YYYY` search criteria for date filtering
- Discovers folders using `getMailboxes('*')`
- Processes each folder independently
- Excellent performance with server-side filtering

#### EWS (Exchange Web Services)
- Uses `IsGreaterThan` restriction on `DateTimeReceived` field
- Discovers folders using `FindFolder` with deep traversal
- Supports both on-premises Exchange and Office 365
- Automatic credential format detection and optimization

#### POP3
- Two-pass processing: header check first, then full download
- Client-side date filtering using message headers
- Only supports INBOX folder (protocol limitation)
- Efficient candidate filtering to minimize downloads

## Retrieving Emails

To retrieve emails with filtering:

```
GET /api/v1/mails?account_id={account_id}&folder=INBOX&is_read=0&per_page=20
```

Available filters:
- `account_id`: Filter by account
- `folder`: Filter by folder
- `is_read`: Filter by read status (0 or 1)
- `is_flagged`: Filter by flagged status (0 or 1)
- `order`: Sort order for date (asc or desc, default: desc)
- `per_page`: Number of results per page (default: 15)

## Testing

The system includes comprehensive test coverage for all email protocols and features.

### Running Tests

#### Using Laravel Sail
```bash
./vendor/bin/sail artisan test
```

#### Standard Installation
```bash
php artisan test
```

### Test Coverage

#### EmailClient Tests (`tests/Feature/EmailClientTest.php`)
- ✅ **IMAP Folder Discovery**: Tests folder enumeration functionality
- ✅ **EWS Folder Discovery**: Tests Exchange folder discovery
- ✅ **POP3 Date Filtering**: Tests client-side date filtering logic
- ✅ **Folder Information Storage**: Verifies correct folder assignment
- ✅ **Response Structure**: Validates API response format
- ✅ **Protocol-Specific Behavior**: Tests each protocol's unique features

#### API Route Tests (`tests/Feature/Api/V1/SimpleApiTest.php`)
- ✅ **Route Existence**: Verifies all API routes are accessible
- ✅ **Model Factories**: Tests Account and Email factory functionality
- ✅ **Validation Rules**: Verifies input validation works correctly
- ✅ **Query Parameters**: Tests filtering and pagination parameters
- ✅ **Database Relationships**: Verifies model relationships work
- ✅ **Data Casting**: Tests proper attribute type casting
- ✅ **JSON Attributes**: Verifies JSON field handling
- ✅ **Soft Deletes**: Tests soft delete functionality
- ✅ **Protocol States**: Tests protocol-specific factory states
- ✅ **Response Format**: Verifies JSON response structure

#### Comprehensive Test Suite (`tests/Feature/Api/V1/`)
For complete API testing including authentication and authorization:
- **AccountsControllerTest.php**: 19 tests covering CRUD operations and permissions
- **MailsControllerTest.php**: 21 tests covering email operations and filtering
- **MiddlewareAndPolicyTest.php**: 12 tests covering security and authorization
- **ValidationTest.php**: 16 tests covering input validation and edge cases

**Total Coverage**: 68+ tests with 200+ assertions covering all API endpoints, validations, and permissions.

See `TESTING_GUIDE.md` for detailed information about running the complete test suite.

#### Model Factories
- **AccountFactory**: Creates test accounts for all protocols (IMAP, POP3, EWS)
- **EmailFactory**: Creates test emails with various attributes and folder assignments

#### Test Database
Tests use Laravel's `RefreshDatabase` trait to ensure clean state between tests.

### Example Test Run
```bash
$ ./vendor/bin/sail artisan test tests/Feature/EmailClientTest.php

   PASS  Tests\Feature\EmailClientTest
  ✓ it can get imap folders
  ✓ it can get ews folders
  ✓ it updates last checked at after checking messages
  ✓ it stores folder information correctly
  ✓ it returns folder results in response
  ✓ it returns pop3 folder results correctly
  ✓ it filters pop3 messages by date

  Tests:    7 passed (29 assertions)
```

### API Route Testing
```bash
$ ./vendor/bin/sail artisan test tests/Feature/Api/V1/SimpleApiTest.php

   PASS  Tests\Feature\Api\V1\SimpleApiTest
  ✓ api routes exist and return proper responses
  ✓ account model factory works
  ✓ email model factory works
  ✓ account validation rules work
  ✓ email filtering query parameters are recognized
  ✓ database relationships work
  ✓ model attributes are properly cast
  ✓ email model json attributes work
  ✓ soft deletes work on models
  ✓ protocol specific account factories work
  ✓ email factory states work
  ✓ api returns json responses
  ✓ database indexes and constraints work

  Tests:    13 passed (61 assertions)
```

### Testing Real Email Connections

For testing with real email servers, create test accounts and update the test configuration:

```php
// Example test with real IMAP server
$account = Account::factory()->create([
    'protocol' => 'imap',
    'server' => 'imap.gmail.com',
    'port' => 993,
    'use_ssl' => true,
    'username' => '<EMAIL>',
    'password' => 'your-app-password',
]);
```

**Note**: Real email server tests require valid credentials and should be run in isolated test environments.

## Performance Considerations

### Optimization Features
- **Date-based filtering** reduces processing time by 60-90% on subsequent runs
- **Folder-specific processing** allows parallel processing opportunities
- **EML file caching** prevents duplicate storage
- **Efficient duplicate detection** using message IDs and UIDs

### Recommended Usage Patterns
- **Initial Sync**: Run during off-peak hours for large mailboxes
- **Regular Checks**: Schedule every 15-30 minutes for active accounts
- **Monitoring**: Use `folder_results` to track processing efficiency

### Scaling Considerations
- Use queue workers for processing multiple accounts
- Consider database indexing on `account_id`, `folder`, and `date` fields
- Monitor S3 storage usage for EML files

## Troubleshooting

### Common Issues

#### IMAP Connection Issues
- Verify server settings and SSL configuration
- Check if IMAP is enabled on the email server
- Ensure correct authentication credentials

#### EWS Authentication Problems
- Try different username formats (domain\user, user@domain)
- Verify Exchange version compatibility
- Check if EWS is enabled on the Exchange server

#### POP3 Limitations
- Remember POP3 only supports INBOX
- Some servers may not provide accurate date headers
- Consider using IMAP for better functionality

### Logging
The system provides detailed logging for troubleshooting:
- Connection attempts and results
- Message processing statistics
- Error details with context
- Performance metrics

## License

Copyrighted for narbulut company