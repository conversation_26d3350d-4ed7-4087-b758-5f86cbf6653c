<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('emails', function (Blueprint $table) {
            $table->id();
            $table->foreignId('account_id')->constrained()->onDelete('cascade');
            $table->string('message_id')->nullable(); // Email message ID
            $table->string('uid')->nullable(); // IMAP/POP3 UID
            $table->string('subject');
            $table->text('body_text')->nullable();
            $table->string('from_email');
            $table->string('from_name')->nullable();
            $table->json('to')->nullable(); // JSON array of recipients
            $table->json('cc')->nullable(); // JSON array of CC recipients
            $table->json('bcc')->nullable(); // JSON array of BCC recipients
            $table->json('attachments')->nullable(); // JSON array of attachment info
            $table->timestamp('date')->nullable(); // Date from email header
            $table->boolean('is_read')->default(false);
            $table->boolean('is_flagged')->default(false);
            $table->string('folder')->default('INBOX');
            $table->string('eml_path')->nullable();
            $table->softDeletes();
            $table->timestamps();            

            // Add indexes for better performance
            $table->index('message_id');
            $table->index('uid');
            $table->index('date');
            $table->index('is_read');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('emails');
    }
};
