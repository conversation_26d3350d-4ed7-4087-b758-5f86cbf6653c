<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('accounts', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('owner_id')->unsigned()->default(0)->index();
            $table->string('name');
            $table->string('email');
            $table->enum('protocol', ['imap', 'pop3', 'ews']);
            $table->string('server');
            $table->integer('port')->unsigned()->nullable();
            $table->string('username');
            $table->string('password');
            $table->boolean('use_ssl')->default(true);
            $table->string('folder')->nullable();
            // EWS specific fields
            $table->string('domain')->nullable();
            $table->string('version')->nullable(); // Exchange version
            $table->boolean('is_active')->default(true);
            $table->integer('checking_period')->unsigned()->default(0);
            $table->timestamp('last_checked_at')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('accounts');
    }
};
