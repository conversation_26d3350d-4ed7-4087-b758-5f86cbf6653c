<?php

namespace Database\Factories;

use App\Models\Account;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Account>
 */
class AccountFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Account::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'owner_id' => 1,
            'name' => $this->faker->company . ' Email',
            'email' => $this->faker->unique()->safeEmail(),
            'protocol' => $this->faker->randomElement(['imap', 'pop3', 'ews']),
            'server' => $this->faker->domainName(),
            'port' => $this->faker->randomElement([993, 995, 143, 110, 443]),
            'username' => $this->faker->userName(),
            'password' => $this->faker->password(),
            'use_ssl' => $this->faker->boolean(80), // 80% chance of using SSL
            'folder' => $this->faker->randomElement(['INBOX', 'Sent', 'Drafts', null]),
            'domain' => $this->faker->optional()->domainName(),
            'version' => $this->faker->optional()->randomElement(['Exchange2013', 'Exchange2016', 'Exchange2019']),
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
            'last_checked_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
        ];
    }

    /**
     * Indicate that the account uses IMAP protocol.
     */
    public function imap(): static
    {
        return $this->state(fn (array $attributes) => [
            'protocol' => 'imap',
            'port' => 993,
            'use_ssl' => true,
            'folder' => 'INBOX',
        ]);
    }

    /**
     * Indicate that the account uses POP3 protocol.
     */
    public function pop3(): static
    {
        return $this->state(fn (array $attributes) => [
            'protocol' => 'pop3',
            'port' => 995,
            'use_ssl' => true,
            'folder' => null, // POP3 doesn't use folders
        ]);
    }

    /**
     * Indicate that the account uses EWS protocol.
     */
    public function ews(): static
    {
        return $this->state(fn (array $attributes) => [
            'protocol' => 'ews',
            'port' => 443,
            'use_ssl' => true,
            'server' => 'https://outlook.office365.com/EWS/Exchange.asmx',
            'domain' => $this->faker->domainName(),
            'version' => 'Exchange2016',
        ]);
    }

    /**
     * Indicate that the account is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the account is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the account has never been checked.
     */
    public function neverChecked(): static
    {
        return $this->state(fn (array $attributes) => [
            'last_checked_at' => null,
        ]);
    }

    /**
     * Indicate that the account was recently checked.
     */
    public function recentlyChecked(): static
    {
        return $this->state(fn (array $attributes) => [
            'last_checked_at' => now()->subMinutes($this->faker->numberBetween(1, 60)),
        ]);
    }
}
