<?php

namespace Database\Factories;

use App\Models\Email;
use App\Models\Account;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Email>
 */
class EmailFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Email::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'message_id' => '<' . $this->faker->uuid() . '@' . $this->faker->domainName() . '>',
            'uid' => $this->faker->unique()->randomNumber(8),
            'subject' => $this->faker->sentence(),
            'body_text' => $this->faker->optional()->text(127), // Limited to 127 chars as per model
            'from_email' => $this->faker->safeEmail(),
            'from_name' => $this->faker->name(),
            'to' => [
                [
                    'email' => $this->faker->safeEmail(),
                    'name' => $this->faker->name(),
                ]
            ],
            'cc' => $this->faker->optional(30)->passthrough([
                [
                    'email' => $this->faker->safeEmail(),
                    'name' => $this->faker->name(),
                ]
            ]),
            'bcc' => $this->faker->optional(10)->passthrough([
                [
                    'email' => $this->faker->safeEmail(),
                    'name' => $this->faker->name(),
                ]
            ]),
            'attachments' => $this->faker->optional(20)->passthrough([
                [
                    'id' => $this->faker->uuid(),
                    'name' => $this->faker->word() . '.pdf',
                    'filename' => '/tmp/' . $this->faker->word() . '.pdf',
                    'size' => $this->faker->numberBetween(1024, 1048576), // 1KB to 1MB
                    'mime' => 'application/pdf',
                ]
            ]),
            'date' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'is_read' => $this->faker->boolean(60), // 60% chance of being read
            'is_flagged' => $this->faker->boolean(10), // 10% chance of being flagged
            'folder' => $this->faker->randomElement(['INBOX', 'Sent', 'Drafts', 'Spam', 'Trash']),
            'eml_path' => $this->faker->optional(80)->passthrough(
                'emails/' . $this->faker->numberBetween(1, 100) . '/' . 
                $this->faker->date() . '_' . $this->faker->lexify('??????????') . '.eml'
            ),
        ];
    }

    /**
     * Indicate that the email is in the INBOX folder.
     */
    public function inbox(): static
    {
        return $this->state(fn (array $attributes) => [
            'folder' => 'INBOX',
        ]);
    }

    /**
     * Indicate that the email is in the Sent folder.
     */
    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'folder' => 'Sent',
        ]);
    }

    /**
     * Indicate that the email is in the Drafts folder.
     */
    public function drafts(): static
    {
        return $this->state(fn (array $attributes) => [
            'folder' => 'Drafts',
        ]);
    }

    /**
     * Indicate that the email is unread.
     */
    public function unread(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_read' => false,
        ]);
    }

    /**
     * Indicate that the email is read.
     */
    public function read(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_read' => true,
        ]);
    }

    /**
     * Indicate that the email is flagged.
     */
    public function flagged(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_flagged' => true,
        ]);
    }

    /**
     * Indicate that the email has attachments.
     */
    public function withAttachments(): static
    {
        return $this->state(fn (array $attributes) => [
            'attachments' => [
                [
                    'id' => $this->faker->uuid(),
                    'name' => 'document.pdf',
                    'filename' => '/tmp/document.pdf',
                    'size' => 524288, // 512KB
                    'mime' => 'application/pdf',
                ],
                [
                    'id' => $this->faker->uuid(),
                    'name' => 'image.jpg',
                    'filename' => '/tmp/image.jpg',
                    'size' => 204800, // 200KB
                    'mime' => 'image/jpeg',
                ],
            ],
        ]);
    }

    /**
     * Indicate that the email has no attachments.
     */
    public function withoutAttachments(): static
    {
        return $this->state(fn (array $attributes) => [
            'attachments' => [],
        ]);
    }

    /**
     * Indicate that the email has an EML file stored.
     */
    public function withEmlFile(): static
    {
        return $this->state(fn (array $attributes) => [
            'eml_path' => 'emails/' . $this->faker->numberBetween(1, 100) . '/' . 
                         $this->faker->date() . '_' . $this->faker->lexify('??????????') . '.eml',
        ]);
    }

    /**
     * Indicate that the email has no EML file stored.
     */
    public function withoutEmlFile(): static
    {
        return $this->state(fn (array $attributes) => [
            'eml_path' => null,
        ]);
    }

    /**
     * Indicate that the email is recent (within last 24 hours).
     */
    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'date' => $this->faker->dateTimeBetween('-24 hours', 'now'),
        ]);
    }

    /**
     * Indicate that the email is old (older than 1 week).
     */
    public function old(): static
    {
        return $this->state(fn (array $attributes) => [
            'date' => $this->faker->dateTimeBetween('-1 year', '-1 week'),
        ]);
    }
}
