<?php

namespace Tests\Feature\Api\V1;

use Tests\TestCase;
use App\Models\Account;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Narbulut\LaravelAuth\Facades\NarAuth;

class ValidationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock authenticated user for all tests
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn(1);
    }

    /** @test */
    public function account_creation_validates_required_fields()
    {
        $response = $this->postJson('/api/v1/accounts', []);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Please check your data'
                ])
                ->assertJsonValidationErrors([
                    'name',
                    'email',
                    'protocol',
                    'server',
                    'port',
                    'username',
                    'password'
                ]);
    }

    /** @test */
    public function account_creation_validates_email_format()
    {
        $invalidEmails = [
            'invalid-email',
            'missing-at-sign.com',
            '@missing-local-part.com',
            'missing-domain@',
            'spaces <EMAIL>',
            'double@@domain.com'
        ];

        foreach ($invalidEmails as $email) {
            $response = $this->postJson('/api/v1/accounts', [
                'name' => 'Test Account',
                'email' => $email,
                'protocol' => 'imap',
                'server' => 'test.com',
                'port' => 993,
                'username' => 'user',
                'password' => 'pass'
            ]);

            $response->assertStatus(422)
                    ->assertJsonValidationErrors(['email']);
        }
    }

    /** @test */
    public function account_creation_validates_email_uniqueness()
    {
        // Create an existing account
        Account::factory()->create(['email' => '<EMAIL>']);

        $response = $this->postJson('/api/v1/accounts', [
            'name' => 'Test Account',
            'email' => '<EMAIL>',
            'protocol' => 'imap',
            'server' => 'test.com',
            'port' => 993,
            'username' => 'user',
            'password' => 'pass'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    /** @test */
    public function account_creation_validates_protocol_values()
    {
        $invalidProtocols = [
            'smtp',
            'http',
            'ftp',
            'invalid',
            'IMAP', // case sensitive
            'Pop3', // case sensitive
            'EWS'   // case sensitive
        ];

        foreach ($invalidProtocols as $protocol) {
            $response = $this->postJson('/api/v1/accounts', [
                'name' => 'Test Account',
                'email' => '<EMAIL>',
                'protocol' => $protocol,
                'server' => 'test.com',
                'port' => 993,
                'username' => 'user',
                'password' => 'pass'
            ]);

            $response->assertStatus(422)
                    ->assertJsonValidationErrors(['protocol']);
        }
    }

    /** @test */
    public function account_creation_validates_port_as_integer()
    {
        $invalidPorts = [
            'not-a-number',
            '993.5',
            'port993',
            '993port',
            '',
            null
        ];

        foreach ($invalidPorts as $port) {
            $response = $this->postJson('/api/v1/accounts', [
                'name' => 'Test Account',
                'email' => '<EMAIL>',
                'protocol' => 'imap',
                'server' => 'test.com',
                'port' => $port,
                'username' => 'user',
                'password' => 'pass'
            ]);

            $response->assertStatus(422)
                    ->assertJsonValidationErrors(['port']);
        }
    }

    /** @test */
    public function account_creation_validates_string_length_limits()
    {
        $longString = str_repeat('a', 256); // 256 characters (over the 255 limit)

        $fields = ['name', 'server', 'username', 'password', 'folder', 'domain', 'version'];

        foreach ($fields as $field) {
            $data = [
                'name' => 'Test Account',
                'email' => '<EMAIL>',
                'protocol' => 'imap',
                'server' => 'test.com',
                'port' => 993,
                'username' => 'user',
                'password' => 'pass'
            ];
            
            $data[$field] = $longString;

            $response = $this->postJson('/api/v1/accounts', $data);

            $response->assertStatus(422)
                    ->assertJsonValidationErrors([$field]);
        }
    }

    /** @test */
    public function account_creation_validates_email_length_limit()
    {
        $longEmail = str_repeat('a', 240) . '@example.com'; // Over 255 characters

        $response = $this->postJson('/api/v1/accounts', [
            'name' => 'Test Account',
            'email' => $longEmail,
            'protocol' => 'imap',
            'server' => 'test.com',
            'port' => 993,
            'username' => 'user',
            'password' => 'pass'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    /** @test */
    public function account_creation_validates_boolean_fields()
    {
        $invalidBooleans = [
            'true',
            'false',
            'yes',
            'no',
            '1',
            '0',
            'on',
            'off'
        ];

        foreach ($invalidBooleans as $value) {
            $response = $this->postJson('/api/v1/accounts', [
                'name' => 'Test Account',
                'email' => '<EMAIL>',
                'protocol' => 'imap',
                'server' => 'test.com',
                'port' => 993,
                'username' => 'user',
                'password' => 'pass',
                'use_ssl' => $value
            ]);

            $response->assertStatus(422)
                    ->assertJsonValidationErrors(['use_ssl']);
        }
    }

    /** @test */
    public function account_creation_accepts_valid_boolean_values()
    {
        $validBooleans = [true, false];

        foreach ($validBooleans as $value) {
            $response = $this->postJson('/api/v1/accounts', [
                'name' => 'Test Account',
                'email' => "test-{$value}@example.com",
                'protocol' => 'imap',
                'server' => 'test.com',
                'port' => 993,
                'username' => 'user',
                'password' => 'pass',
                'use_ssl' => $value
            ]);

            $response->assertStatus(201);
        }
    }

    /** @test */
    public function account_creation_allows_nullable_fields()
    {
        $response = $this->postJson('/api/v1/accounts', [
            'name' => 'Test Account',
            'email' => '<EMAIL>',
            'protocol' => 'imap',
            'server' => 'test.com',
            'port' => 993,
            'username' => 'user',
            'password' => 'pass',
            'folder' => null,
            'domain' => null,
            'version' => null
        ]);

        $response->assertStatus(201);
    }

    /** @test */
    public function account_update_validation_is_less_strict()
    {
        $account = Account::factory()->create(['owner_id' => 1]);

        // All fields are optional for updates
        $response = $this->putJson("/api/v1/accounts/{$account->id}", [
            'name' => 'Updated Name'
        ]);

        $response->assertStatus(200);

        // But invalid values should still be rejected
        $response = $this->putJson("/api/v1/accounts/{$account->id}", [
            'protocol' => 'invalid'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['protocol']);
    }

    /** @test */
    public function account_update_validates_data_types()
    {
        $account = Account::factory()->create(['owner_id' => 1]);

        $invalidUpdates = [
            ['port' => 'not-a-number'],
            ['use_ssl' => 'not-boolean'],
            ['is_active' => 'not-boolean'],
            ['protocol' => 'invalid-protocol']
        ];

        foreach ($invalidUpdates as $updateData) {
            $response = $this->putJson("/api/v1/accounts/{$account->id}", $updateData);

            $response->assertStatus(422);
            $field = array_key_first($updateData);
            $response->assertJsonValidationErrors([$field]);
        }
    }

    /** @test */
    public function validation_error_response_format_is_consistent()
    {
        $response = $this->postJson('/api/v1/accounts', [
            'email' => 'invalid-email',
            'protocol' => 'invalid-protocol'
        ]);

        $response->assertStatus(422)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'name',
                        'email',
                        'protocol',
                        'server',
                        'port',
                        'username',
                        'password'
                    ]
                ])
                ->assertJson([
                    'success' => false,
                    'message' => 'Please check your data'
                ]);

        // Ensure error messages are arrays
        $errors = $response->json('data');
        foreach ($errors as $field => $messages) {
            $this->assertIsArray($messages);
            $this->assertNotEmpty($messages);
        }
    }

    /** @test */
    public function edge_case_port_values_are_handled()
    {
        $edgeCasePorts = [
            -1,      // Negative
            0,       // Zero
            65536,   // Above valid port range
            999999   // Way above valid range
        ];

        foreach ($edgeCasePorts as $port) {
            $response = $this->postJson('/api/v1/accounts', [
                'name' => 'Test Account',
                'email' => "test-{$port}@example.com",
                'protocol' => 'imap',
                'server' => 'test.com',
                'port' => $port,
                'username' => 'user',
                'password' => 'pass'
            ]);

            // The validation should accept these as integers
            // but the application logic might want to add additional validation
            $response->assertStatus(201);
        }
    }

    /** @test */
    public function special_characters_in_fields_are_handled()
    {
        $specialCharacters = [
            'name' => 'Test Account with émojis 🚀 and spëcial chars',
            'server' => 'test-server.example.com',
            'username' => '<EMAIL>',
            'password' => 'P@ssw0rd!@#$%^&*()',
            'folder' => 'INBOX/Spëcial Földer',
            'domain' => 'DOMAIN\\User'
        ];

        $response = $this->postJson('/api/v1/accounts', array_merge([
            'email' => '<EMAIL>',
            'protocol' => 'imap',
            'port' => 993
        ], $specialCharacters));

        $response->assertStatus(201);
    }
}
