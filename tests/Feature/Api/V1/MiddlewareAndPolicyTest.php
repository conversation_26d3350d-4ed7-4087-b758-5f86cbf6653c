<?php

namespace Tests\Feature\Api\V1;

use Tests\TestCase;
use App\Models\Account;
use App\Models\Email;
use App\Services\EmailClient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Narbulut\LaravelAuth\Facades\NarAuth;
use Mockery;

class MiddlewareAndPolicyTest extends TestCase
{
    use RefreshDatabase;

    protected $emailClient;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock the EmailClient service
        $this->emailClient = Mockery::mock(EmailClient::class);
        $this->app->instance(EmailClient::class, $this->emailClient);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function account_owner_middleware_blocks_unauthenticated_users()
    {
        // Mock unauthenticated user
        NarAuth::shouldReceive('check')->andReturn(false);
        NarAuth::shouldReceive('id')->andReturn(null);

        $account = Account::factory()->create();

        $routes = [
            ['method' => 'get', 'url' => '/api/v1/accounts'],
            ['method' => 'post', 'url' => '/api/v1/accounts'],
            ['method' => 'get', 'url' => "/api/v1/accounts/{$account->id}"],
            ['method' => 'put', 'url' => "/api/v1/accounts/{$account->id}"],
            ['method' => 'delete', 'url' => "/api/v1/accounts/{$account->id}"],
            ['method' => 'get', 'url' => "/api/v1/accounts/{$account->id}/test"],
            ['method' => 'get', 'url' => "/api/v1/accounts/{$account->id}/check"],
            ['method' => 'get', 'url' => "/api/v1/mails/{$account->id}"],
        ];

        foreach ($routes as $route) {
            $response = $this->{$route['method'] . 'Json'}($route['url'], []);
            $response->assertStatus(401)
                    ->assertJson(['error' => 'Unauthorized']);
        }
    }

    /** @test */
    public function account_owner_middleware_blocks_access_to_other_users_accounts()
    {
        $userId = 1;
        $otherUserId = 2;
        
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $otherAccount = Account::factory()->create(['owner_id' => $otherUserId]);

        $routes = [
            ['method' => 'get', 'url' => "/api/v1/accounts/{$otherAccount->id}"],
            ['method' => 'put', 'url' => "/api/v1/accounts/{$otherAccount->id}"],
            ['method' => 'delete', 'url' => "/api/v1/accounts/{$otherAccount->id}"],
            ['method' => 'get', 'url' => "/api/v1/accounts/{$otherAccount->id}/test"],
            ['method' => 'get', 'url' => "/api/v1/accounts/{$otherAccount->id}/check"],
            ['method' => 'get', 'url' => "/api/v1/mails/{$otherAccount->id}"],
        ];

        foreach ($routes as $route) {
            $response = $this->{$route['method'] . 'Json'}($route['url'], []);
            $response->assertStatus(403)
                    ->assertJson(['error' => 'Forbidden']);
        }
    }

    /** @test */
    public function account_policy_allows_owner_access()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);

        // Mock EmailClient for test and check endpoints
        $this->emailClient->shouldReceive('testConnection')->andReturn(true);
        $this->emailClient->shouldReceive('checkNewMessages')->andReturn([
            'success' => true,
            'new_messages' => 0
        ]);

        $routes = [
            ['method' => 'get', 'url' => '/api/v1/accounts'],
            ['method' => 'get', 'url' => "/api/v1/accounts/{$account->id}"],
            ['method' => 'put', 'url' => "/api/v1/accounts/{$account->id}"],
            ['method' => 'delete', 'url' => "/api/v1/accounts/{$account->id}"],
            ['method' => 'get', 'url' => "/api/v1/accounts/{$account->id}/test"],
            ['method' => 'get', 'url' => "/api/v1/accounts/{$account->id}/check"],
        ];

        foreach ($routes as $route) {
            $response = $this->{$route['method'] . 'Json'}($route['url'], []);
            $response->assertStatus(200);
        }
    }

    /** @test */
    public function email_policy_allows_owner_access()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);
        $email = Email::factory()->create(['account_id' => $account->id]);

        $routes = [
            ['method' => 'get', 'url' => "/api/v1/mails/{$account->id}"],
            ['method' => 'get', 'url' => "/api/v1/mails/{$account->id}/{$email->id}"],
            ['method' => 'delete', 'url' => "/api/v1/mails/{$account->id}/{$email->id}"],
        ];

        foreach ($routes as $route) {
            $response = $this->{$route['method'] . 'Json'}($route['url'], []);
            $response->assertStatus(200);
        }
    }

    /** @test */
    public function email_policy_blocks_access_to_other_users_emails()
    {
        $userId = 1;
        $otherUserId = 2;
        
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $otherAccount = Account::factory()->create(['owner_id' => $otherUserId]);
        $otherEmail = Email::factory()->create(['account_id' => $otherAccount->id]);

        $routes = [
            ['method' => 'get', 'url' => "/api/v1/mails/{$otherAccount->id}"],
            ['method' => 'get', 'url' => "/api/v1/mails/{$otherAccount->id}/{$otherEmail->id}"],
            ['method' => 'delete', 'url' => "/api/v1/mails/{$otherAccount->id}/{$otherEmail->id}"],
        ];

        foreach ($routes as $route) {
            $response = $this->{$route['method'] . 'Json'}($route['url'], []);
            $response->assertStatus(403)
                    ->assertJson(['error' => 'Forbidden']);
        }
    }

    /** @test */
    public function account_creation_automatically_sets_owner_id()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $accountData = [
            'name' => 'Test Account',
            'email' => '<EMAIL>',
            'protocol' => 'imap',
            'server' => 'imap.example.com',
            'port' => 993,
            'username' => 'testuser',
            'password' => 'testpass'
        ];

        $response = $this->postJson('/api/v1/accounts', $accountData);

        $response->assertStatus(201);
        
        $account = Account::where('email', '<EMAIL>')->first();
        $this->assertEquals($userId, $account->owner_id);
    }

    /** @test */
    public function user_cannot_manipulate_owner_id_during_creation()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $accountData = [
            'name' => 'Test Account',
            'email' => '<EMAIL>',
            'protocol' => 'imap',
            'server' => 'imap.example.com',
            'port' => 993,
            'username' => 'testuser',
            'password' => 'testpass',
            'owner_id' => 999 // Trying to set a different owner_id
        ];

        $response = $this->postJson('/api/v1/accounts', $accountData);

        $response->assertStatus(201);
        
        $account = Account::where('email', '<EMAIL>')->first();
        // Should be set to the authenticated user's ID, not the provided one
        $this->assertEquals($userId, $account->owner_id);
        $this->assertNotEquals(999, $account->owner_id);
    }

    /** @test */
    public function user_cannot_manipulate_owner_id_during_update()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);

        $updateData = [
            'name' => 'Updated Account',
            'owner_id' => 999 // Trying to change owner_id
        ];

        $response = $this->putJson("/api/v1/accounts/{$account->id}", $updateData);

        $response->assertStatus(200);
        
        $account->refresh();
        // owner_id should remain unchanged
        $this->assertEquals($userId, $account->owner_id);
        $this->assertNotEquals(999, $account->owner_id);
        // But name should be updated
        $this->assertEquals('Updated Account', $account->name);
    }

    /** @test */
    public function middleware_and_policy_work_together_for_double_protection()
    {
        $userId = 1;
        $otherUserId = 2;
        
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $userAccount = Account::factory()->create(['owner_id' => $userId]);
        $otherAccount = Account::factory()->create(['owner_id' => $otherUserId]);
        
        $userEmail = Email::factory()->create(['account_id' => $userAccount->id]);
        $otherEmail = Email::factory()->create(['account_id' => $otherAccount->id]);

        // User can access their own resources
        $response = $this->getJson("/api/v1/accounts/{$userAccount->id}");
        $response->assertStatus(200);

        $response = $this->getJson("/api/v1/mails/{$userAccount->id}");
        $response->assertStatus(200);

        $response = $this->getJson("/api/v1/mails/{$userAccount->id}/{$userEmail->id}");
        $response->assertStatus(200);

        // User cannot access other user's resources (blocked by middleware)
        $response = $this->getJson("/api/v1/accounts/{$otherAccount->id}");
        $response->assertStatus(403);

        $response = $this->getJson("/api/v1/mails/{$otherAccount->id}");
        $response->assertStatus(403);

        $response = $this->getJson("/api/v1/mails/{$otherAccount->id}/{$otherEmail->id}");
        $response->assertStatus(403);
    }

    /** @test */
    public function cross_account_email_access_is_prevented()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account1 = Account::factory()->create(['owner_id' => $userId]);
        $account2 = Account::factory()->create(['owner_id' => $userId]);
        
        $email1 = Email::factory()->create(['account_id' => $account1->id]);

        // Try to access email from account1 using account2's route
        // This should fail because the email doesn't belong to account2
        $response = $this->getJson("/api/v1/mails/{$account2->id}/{$email1->id}");
        $response->assertStatus(404); // Email not found in account2

        // But accessing it through the correct account should work
        $response = $this->getJson("/api/v1/mails/{$account1->id}/{$email1->id}");
        $response->assertStatus(200);
    }

    /** @test */
    public function api_error_handling_returns_proper_json_responses()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        // Test 404 for non-existent account
        $response = $this->getJson('/api/v1/accounts/999999');
        $response->assertStatus(404)
                ->assertJson(['message' => 'Record not found.'])
                ->assertHeader('Content-Type', 'application/json');

        // Test 404 for non-existent email
        $account = Account::factory()->create(['owner_id' => $userId]);
        $response = $this->getJson("/api/v1/mails/{$account->id}/999999");
        $response->assertStatus(404)
                ->assertJson(['message' => 'Record not found.'])
                ->assertHeader('Content-Type', 'application/json');
    }
}
