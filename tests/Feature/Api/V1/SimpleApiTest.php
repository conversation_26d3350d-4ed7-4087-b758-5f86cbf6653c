<?php

namespace Tests\Feature\Api\V1;

use Tests\TestCase;
use App\Models\Account;
use App\Models\Email;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SimpleApiTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function api_routes_exist_and_return_proper_responses()
    {
        // Test that routes exist and return expected status codes
        $routes = [
            ['method' => 'get', 'url' => '/api/v1/accounts', 'expected_status' => [401, 403]], // Unauthorized or Forbidden
            ['method' => 'post', 'url' => '/api/v1/accounts', 'expected_status' => [401, 403, 422]], // Validation or Auth error
        ];

        foreach ($routes as $route) {
            $response = $this->{$route['method'] . 'Json'}($route['url'], []);
            $this->assertContains($response->getStatusCode(), $route['expected_status'], 
                "Route {$route['method']} {$route['url']} returned unexpected status");
        }
    }

    /** @test */
    public function account_model_factory_works()
    {
        $account = Account::factory()->create();
        
        $this->assertDatabaseHas('accounts', [
            'id' => $account->id,
            'email' => $account->email
        ]);
        
        $this->assertNotNull($account->name);
        $this->assertNotNull($account->protocol);
        $this->assertIsInt($account->port);
    }

    /** @test */
    public function email_model_factory_works()
    {
        $account = Account::factory()->create();
        $email = Email::factory()->create(['account_id' => $account->id]);
        
        $this->assertDatabaseHas('emails', [
            'id' => $email->id,
            'account_id' => $account->id
        ]);
        
        $this->assertNotNull($email->subject);
        $this->assertNotNull($email->from_email);
        $this->assertNotNull($email->folder);
    }

    /** @test */
    public function account_validation_rules_work()
    {
        // Test validation without authentication to focus on validation logic
        $response = $this->postJson('/api/v1/accounts', []);
        
        // Should return validation error (422) or authentication error (401)
        $this->assertContains($response->getStatusCode(), [401, 422]);
        
        if ($response->getStatusCode() === 422) {
            // If we get validation error, check the structure
            $response->assertJsonStructure([
                'success',
                'message',
                'data'
            ]);
        }
    }

    /** @test */
    public function email_filtering_query_parameters_are_recognized()
    {
        $account = Account::factory()->create();
        
        // Test that the routes accept query parameters without errors
        $filterRoutes = [
            "/api/v1/mails/{$account->id}?folder=INBOX",
            "/api/v1/mails/{$account->id}?is_read=1",
            "/api/v1/mails/{$account->id}?is_flagged=0",
            "/api/v1/mails/{$account->id}?order=asc",
            "/api/v1/mails/{$account->id}?per_page=10",
        ];

        foreach ($filterRoutes as $url) {
            $response = $this->getJson($url);
            // Should return auth error (401/403) but not 404 (route not found)
            $this->assertContains($response->getStatusCode(), [401, 403], 
                "Route {$url} should exist and return auth error, not 404");
        }
    }

    /** @test */
    public function database_relationships_work()
    {
        $account = Account::factory()->create();
        $emails = Email::factory()->count(3)->create(['account_id' => $account->id]);
        
        // Test account->emails relationship
        $this->assertEquals(3, $account->emails()->count());
        
        // Test email->account relationship
        $email = $emails->first();
        $this->assertEquals($account->id, $email->account->id);
    }

    /** @test */
    public function model_attributes_are_properly_cast()
    {
        $account = Account::factory()->create([
            'use_ssl' => true,
            'is_active' => false,
            'port' => 993
        ]);
        
        // Test boolean casting
        $this->assertIsBool($account->use_ssl);
        $this->assertIsBool($account->is_active);
        $this->assertTrue($account->use_ssl);
        $this->assertFalse($account->is_active);
        
        // Test integer casting
        $this->assertIsInt($account->port);
        $this->assertEquals(993, $account->port);
    }

    /** @test */
    public function email_model_json_attributes_work()
    {
        $email = Email::factory()->create([
            'to' => [
                ['email' => '<EMAIL>', 'name' => 'Test User 1'],
                ['email' => '<EMAIL>', 'name' => 'Test User 2']
            ],
            'attachments' => [
                ['name' => 'document.pdf', 'size' => 1024]
            ]
        ]);
        
        // Test JSON attribute casting
        $this->assertIsArray($email->to);
        $this->assertIsArray($email->attachments);
        $this->assertCount(2, $email->to);
        $this->assertCount(1, $email->attachments);
        
        // Test specific values
        $this->assertEquals('<EMAIL>', $email->to[0]['email']);
        $this->assertEquals('document.pdf', $email->attachments[0]['name']);
    }

    /** @test */
    public function soft_deletes_work_on_models()
    {
        $account = Account::factory()->create();
        $email = Email::factory()->create(['account_id' => $account->id]);
        
        // Delete models
        $account->delete();
        $email->delete();
        
        // Should be soft deleted (not actually removed from database)
        $this->assertSoftDeleted('accounts', ['id' => $account->id]);
        $this->assertSoftDeleted('emails', ['id' => $email->id]);
        
        // Should not appear in normal queries
        $this->assertEquals(0, Account::count());
        $this->assertEquals(0, Email::count());
        
        // Should appear in withTrashed queries
        $this->assertEquals(1, Account::withTrashed()->count());
        $this->assertEquals(1, Email::withTrashed()->count());
    }

    /** @test */
    public function protocol_specific_account_factories_work()
    {
        $imapAccount = Account::factory()->imap()->create();
        $pop3Account = Account::factory()->pop3()->create();
        $ewsAccount = Account::factory()->ews()->create();
        
        $this->assertEquals('imap', $imapAccount->protocol);
        $this->assertEquals('pop3', $pop3Account->protocol);
        $this->assertEquals('ews', $ewsAccount->protocol);
        
        // Test protocol-specific defaults
        $this->assertEquals(993, $imapAccount->port);
        $this->assertEquals(995, $pop3Account->port);
        $this->assertEquals(443, $ewsAccount->port);
        
        $this->assertTrue($imapAccount->use_ssl);
        $this->assertTrue($pop3Account->use_ssl);
        $this->assertTrue($ewsAccount->use_ssl);
    }

    /** @test */
    public function email_factory_states_work()
    {
        $account = Account::factory()->create();
        
        $inboxEmail = Email::factory()->inbox()->create(['account_id' => $account->id]);
        $sentEmail = Email::factory()->sent()->create(['account_id' => $account->id]);
        $unreadEmail = Email::factory()->unread()->create(['account_id' => $account->id]);
        $flaggedEmail = Email::factory()->flagged()->create(['account_id' => $account->id]);
        
        $this->assertEquals('INBOX', $inboxEmail->folder);
        $this->assertEquals('Sent', $sentEmail->folder);
        $this->assertFalse($unreadEmail->is_read);
        $this->assertTrue($flaggedEmail->is_flagged);
    }

    /** @test */
    public function api_returns_json_responses()
    {
        $account = Account::factory()->create();
        
        $routes = [
            ['method' => 'get', 'url' => '/api/v1/accounts'],
            ['method' => 'post', 'url' => '/api/v1/accounts'],
            ['method' => 'get', 'url' => "/api/v1/accounts/{$account->id}"],
            ['method' => 'get', 'url' => "/api/v1/mails/{$account->id}"],
        ];

        foreach ($routes as $route) {
            $response = $this->{$route['method'] . 'Json'}($route['url'], []);
            
            // Should return JSON content type
            $this->assertEquals('application/json', $response->headers->get('Content-Type'));
            
            // Should be valid JSON
            $this->assertJson($response->getContent());
        }
    }

    /** @test */
    public function database_indexes_and_constraints_work()
    {
        // Test unique email constraint
        $account1 = Account::factory()->create(['email' => '<EMAIL>']);
        
        // This should work fine
        $this->assertDatabaseHas('accounts', ['email' => '<EMAIL>']);
        
        // Test foreign key constraint
        $email = Email::factory()->create(['account_id' => $account1->id]);
        $this->assertEquals($account1->id, $email->account_id);
        
        // Test that we can query by indexed fields efficiently
        $foundAccount = Account::where('email', '<EMAIL>')->first();
        $this->assertEquals($account1->id, $foundAccount->id);
        
        $foundEmails = Email::where('account_id', $account1->id)->get();
        $this->assertCount(1, $foundEmails);
    }
}
