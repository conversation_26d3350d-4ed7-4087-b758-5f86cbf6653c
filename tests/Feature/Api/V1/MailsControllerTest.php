<?php

namespace Tests\Feature\Api\V1;

use Tests\TestCase;
use App\Models\Account;
use App\Models\Email;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Storage;
use Narbulut\LaravelAuth\Facades\NarAuth;

class MailsControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock S3 storage
        Storage::fake('s3');
    }

    /** @test */
    public function unauthenticated_user_cannot_access_emails()
    {
        // Mock unauthenticated user
        NarAuth::shouldReceive('check')->andReturn(false);
        NarAuth::shouldReceive('id')->andReturn(null);

        $account = Account::factory()->create();

        $response = $this->getJson("/api/v1/mails/{$account->id}");

        $response->assertStatus(401)
                ->assertJson(['error' => 'Unauthorized']);
    }

    /** @test */
    public function user_can_list_emails_from_their_own_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);
        $emails = Email::factory()->count(5)->create(['account_id' => $account->id]);

        $response = $this->getJson("/api/v1/mails/{$account->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ])
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'data',
                        'links',
                        'meta'
                    ]
                ]);

        $this->assertEquals(5, count($response->json('data.data')));
    }

    /** @test */
    public function user_cannot_list_emails_from_other_users_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $otherAccount = Account::factory()->create(['owner_id' => 2]);
        Email::factory()->count(3)->create(['account_id' => $otherAccount->id]);

        $response = $this->getJson("/api/v1/mails/{$otherAccount->id}");

        $response->assertStatus(403)
                ->assertJson(['error' => 'Forbidden']);
    }

    /** @test */
    public function email_listing_supports_folder_filtering()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);

        // Create emails in different folders
        Email::factory()->count(3)->create([
            'account_id' => $account->id,
            'folder' => 'INBOX'
        ]);
        Email::factory()->count(2)->create([
            'account_id' => $account->id,
            'folder' => 'Sent'
        ]);

        // Test filtering by INBOX
        $response = $this->getJson("/api/v1/mails/{$account->id}?folder=INBOX");
        $response->assertStatus(200);
        $this->assertEquals(3, count($response->json('data.data')));

        // Test filtering by Sent
        $response = $this->getJson("/api/v1/mails/{$account->id}?folder=Sent");
        $response->assertStatus(200);
        $this->assertEquals(2, count($response->json('data.data')));
    }

    /** @test */
    public function email_listing_supports_read_status_filtering()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);

        // Create read and unread emails
        Email::factory()->count(3)->create([
            'account_id' => $account->id,
            'is_read' => true
        ]);
        Email::factory()->count(2)->create([
            'account_id' => $account->id,
            'is_read' => false
        ]);

        // Test filtering by read emails
        $response = $this->getJson("/api/v1/mails/{$account->id}?is_read=1");
        $response->assertStatus(200);
        $this->assertEquals(3, count($response->json('data.data')));

        // Test filtering by unread emails
        $response = $this->getJson("/api/v1/mails/{$account->id}?is_read=0");
        $response->assertStatus(200);
        $this->assertEquals(2, count($response->json('data.data')));
    }

    /** @test */
    public function email_listing_supports_flagged_status_filtering()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);

        // Create flagged and unflagged emails
        Email::factory()->count(2)->create([
            'account_id' => $account->id,
            'is_flagged' => true
        ]);
        Email::factory()->count(3)->create([
            'account_id' => $account->id,
            'is_flagged' => false
        ]);

        // Test filtering by flagged emails
        $response = $this->getJson("/api/v1/mails/{$account->id}?is_flagged=1");
        $response->assertStatus(200);
        $this->assertEquals(2, count($response->json('data.data')));

        // Test filtering by unflagged emails
        $response = $this->getJson("/api/v1/mails/{$account->id}?is_flagged=0");
        $response->assertStatus(200);
        $this->assertEquals(3, count($response->json('data.data')));
    }

    /** @test */
    public function email_listing_supports_ordering()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);

        // Create emails with different dates
        $oldEmail = Email::factory()->create([
            'account_id' => $account->id,
            'date' => now()->subDays(2),
            'subject' => 'Old Email'
        ]);
        $newEmail = Email::factory()->create([
            'account_id' => $account->id,
            'date' => now()->subDays(1),
            'subject' => 'New Email'
        ]);

        // Test default ordering (desc - newest first)
        $response = $this->getJson("/api/v1/mails/{$account->id}");
        $response->assertStatus(200);
        $emails = $response->json('data.data');
        $this->assertEquals('New Email', $emails[0]['subject']);

        // Test ascending order (oldest first)
        $response = $this->getJson("/api/v1/mails/{$account->id}?order=asc");
        $response->assertStatus(200);
        $emails = $response->json('data.data');
        $this->assertEquals('Old Email', $emails[0]['subject']);
    }

    /** @test */
    public function email_listing_supports_pagination()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);
        Email::factory()->count(25)->create(['account_id' => $account->id]);

        // Test default pagination (15 per page)
        $response = $this->getJson("/api/v1/mails/{$account->id}");
        $response->assertStatus(200);
        $this->assertEquals(15, count($response->json('data.data')));

        // Test custom pagination
        $response = $this->getJson("/api/v1/mails/{$account->id}?per_page=10");
        $response->assertStatus(200);
        $this->assertEquals(10, count($response->json('data.data')));
    }

    /** @test */
    public function user_can_view_email_from_their_own_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);
        $email = Email::factory()->create([
            'account_id' => $account->id,
            'is_read' => false
        ]);

        $response = $this->getJson("/api/v1/mails/{$account->id}/{$email->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'id' => $email->id,
                        'subject' => $email->subject,
                        'from_email' => $email->from_email
                    ]
                ]);

        // Email should be marked as read
        $this->assertDatabaseHas('emails', [
            'id' => $email->id,
            'is_read' => true
        ]);
    }

    /** @test */
    public function user_cannot_view_email_from_other_users_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $otherAccount = Account::factory()->create(['owner_id' => 2]);
        $email = Email::factory()->create(['account_id' => $otherAccount->id]);

        $response = $this->getJson("/api/v1/mails/{$otherAccount->id}/{$email->id}");

        $response->assertStatus(403)
                ->assertJson(['error' => 'Forbidden']);
    }

    /** @test */
    public function user_can_view_raw_email_from_their_own_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);
        $email = Email::factory()->create([
            'account_id' => $account->id,
            'eml_path' => 'emails/test.eml'
        ]);

        // Create fake EML content
        $emlContent = "From: <EMAIL>\nTo: <EMAIL>\nSubject: Test Email\n\nThis is a test email.";
        Storage::disk('s3')->put('emails/test.eml', $emlContent);

        $response = $this->get("/api/v1/mails/{$account->id}/{$email->id}/raw");

        $response->assertStatus(200);
        $this->assertEquals($emlContent, $response->getContent());
    }

    /** @test */
    public function user_cannot_view_raw_email_from_other_users_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $otherAccount = Account::factory()->create(['owner_id' => 2]);
        $email = Email::factory()->create([
            'account_id' => $otherAccount->id,
            'eml_path' => 'emails/test.eml'
        ]);

        $response = $this->get("/api/v1/mails/{$otherAccount->id}/{$email->id}/raw");

        $response->assertStatus(403)
                ->assertJson(['error' => 'Forbidden']);
    }

    /** @test */
    public function user_can_delete_email_from_their_own_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);
        $email = Email::factory()->create(['account_id' => $account->id]);

        $response = $this->deleteJson("/api/v1/mails/{$account->id}/{$email->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Email deleted successfully'
                ]);

        $this->assertSoftDeleted('emails', ['id' => $email->id]);
    }

    /** @test */
    public function user_cannot_delete_email_from_other_users_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $otherAccount = Account::factory()->create(['owner_id' => 2]);
        $email = Email::factory()->create(['account_id' => $otherAccount->id]);

        $response = $this->deleteJson("/api/v1/mails/{$otherAccount->id}/{$email->id}");

        $response->assertStatus(403)
                ->assertJson(['error' => 'Forbidden']);

        $this->assertDatabaseHas('emails', ['id' => $email->id]);
    }

    /** @test */
    public function email_not_found_returns_404()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);

        $response = $this->getJson("/api/v1/mails/{$account->id}/999999");

        $response->assertStatus(404)
                ->assertJson(['message' => 'Record not found.']);
    }

    /** @test */
    public function account_not_found_returns_404()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $response = $this->getJson('/api/v1/mails/999999');

        $response->assertStatus(404)
                ->assertJson(['message' => 'Record not found.']);
    }

    /** @test */
    public function email_listing_with_multiple_filters_works()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);

        // Create emails with different combinations
        Email::factory()->create([
            'account_id' => $account->id,
            'folder' => 'INBOX',
            'is_read' => false,
            'is_flagged' => true
        ]);
        Email::factory()->create([
            'account_id' => $account->id,
            'folder' => 'INBOX',
            'is_read' => true,
            'is_flagged' => false
        ]);
        Email::factory()->create([
            'account_id' => $account->id,
            'folder' => 'Sent',
            'is_read' => false,
            'is_flagged' => true
        ]);

        // Test multiple filters
        $response = $this->getJson("/api/v1/mails/{$account->id}?folder=INBOX&is_read=0&is_flagged=1");
        $response->assertStatus(200);
        $this->assertEquals(1, count($response->json('data.data')));
    }

    /** @test */
    public function viewing_already_read_email_does_not_change_read_status()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);
        $email = Email::factory()->create([
            'account_id' => $account->id,
            'is_read' => true
        ]);

        $response = $this->getJson("/api/v1/mails/{$account->id}/{$email->id}");

        $response->assertStatus(200);

        // Email should still be marked as read
        $this->assertDatabaseHas('emails', [
            'id' => $email->id,
            'is_read' => true
        ]);
    }

    /** @test */
    public function email_belongs_to_correct_account_validation()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account1 = Account::factory()->create(['owner_id' => $userId]);
        $account2 = Account::factory()->create(['owner_id' => $userId]);

        $email = Email::factory()->create(['account_id' => $account1->id]);

        // Try to access email from account1 using account2's route
        $response = $this->getJson("/api/v1/mails/{$account2->id}/{$email->id}");

        $response->assertStatus(404)
                ->assertJson(['message' => 'Record not found.']);
    }

    /** @test */
    public function raw_email_with_missing_eml_file_handles_gracefully()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);
        $email = Email::factory()->create([
            'account_id' => $account->id,
            'eml_path' => 'emails/nonexistent.eml'
        ]);

        $response = $this->get("/api/v1/mails/{$account->id}/{$email->id}/raw");

        // This should handle the missing file gracefully
        // The exact behavior depends on how Storage::disk('s3')->get() handles missing files
        $response->assertStatus(500); // Or whatever status your app returns for missing files
    }

    /** @test */
    public function complex_filtering_scenario()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);

        // Create a complex set of emails
        // INBOX, unread, flagged
        Email::factory()->create([
            'account_id' => $account->id,
            'folder' => 'INBOX',
            'is_read' => false,
            'is_flagged' => true,
            'date' => now()->subDays(1)
        ]);
        // INBOX, read, not flagged
        Email::factory()->create([
            'account_id' => $account->id,
            'folder' => 'INBOX',
            'is_read' => true,
            'is_flagged' => false,
            'date' => now()->subDays(2)
        ]);
        // Sent, unread, not flagged
        Email::factory()->create([
            'account_id' => $account->id,
            'folder' => 'Sent',
            'is_read' => false,
            'is_flagged' => false,
            'date' => now()->subDays(3)
        ]);

        // Test various filter combinations
        $testCases = [
            ['filter' => 'folder=INBOX', 'expected_count' => 2],
            ['filter' => 'is_read=0', 'expected_count' => 2],
            ['filter' => 'is_flagged=1', 'expected_count' => 1],
            ['filter' => 'folder=INBOX&is_read=0', 'expected_count' => 1],
            ['filter' => 'folder=INBOX&is_flagged=1', 'expected_count' => 1],
            ['filter' => 'is_read=0&is_flagged=0', 'expected_count' => 1],
            ['filter' => 'folder=Sent&is_read=0&is_flagged=0', 'expected_count' => 1],
        ];

        foreach ($testCases as $testCase) {
            $response = $this->getJson("/api/v1/mails/{$account->id}?{$testCase['filter']}");
            $response->assertStatus(200);
            $this->assertEquals(
                $testCase['expected_count'],
                count($response->json('data.data')),
                "Failed for filter: {$testCase['filter']}"
            );
        }
    }

    /** @test */
    public function pagination_metadata_is_correct()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);
        Email::factory()->count(25)->create(['account_id' => $account->id]);

        $response = $this->getJson("/api/v1/mails/{$account->id}?per_page=10");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'data',
                        'links' => [
                            'first',
                            'last',
                            'prev',
                            'next'
                        ],
                        'meta' => [
                            'current_page',
                            'from',
                            'last_page',
                            'per_page',
                            'to',
                            'total'
                        ]
                    ]
                ]);

        $meta = $response->json('data.meta');
        $this->assertEquals(25, $meta['total']);
        $this->assertEquals(10, $meta['per_page']);
        $this->assertEquals(3, $meta['last_page']);
    }
}
