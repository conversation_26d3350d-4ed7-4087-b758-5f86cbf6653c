<?php

namespace Tests\Feature\Api\V1;

use Tests\TestCase;
use App\Models\Account;
use App\Services\EmailClient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Narbulut\LaravelAuth\Facades\NarAuth;
use Mockery;

class AccountsControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $emailClient;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the EmailClient service
        $this->emailClient = Mockery::mock(EmailClient::class);
        $this->app->instance(EmailClient::class, $this->emailClient);

        // Mock the Auth facade to avoid conflicts with <PERSON><PERSON>'s authorization
        $this->app['auth']->shouldReceive('userResolver')->andReturn(function () {
            return null; // Return null for unauthenticated, or a user object for authenticated
        });
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function unauthenticated_user_cannot_access_accounts()
    {
        // Mock unauthenticated user
        NarAuth::shouldR<PERSON>eive('check')->andReturn(false);
        NarAuth::shouldReceive('id')->andReturn(null);

        $response = $this->getJson('/api/v1/accounts');

        $response->assertStatus(401)
                ->assertJson(['error' => 'Unauthorized']);
    }

    /** @test */
    public function authenticated_user_can_list_their_accounts()
    {
        // Mock authenticated user
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        // Create accounts for the user and another user
        $userAccounts = Account::factory()->count(3)->create(['owner_id' => $userId]);
        $otherAccounts = Account::factory()->count(2)->create(['owner_id' => 2]);

        $response = $this->getJson('/api/v1/accounts');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data',
                    'links',
                    'meta'
                ]);

        // Should only see their own accounts
        $this->assertEquals(3, count($response->json('data')));
    }

    /** @test */
    public function user_can_create_account_with_valid_data()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $accountData = [
            'name' => 'Test Account',
            'email' => '<EMAIL>',
            'protocol' => 'imap',
            'server' => 'imap.example.com',
            'port' => 993,
            'username' => 'testuser',
            'password' => 'testpass',
            'use_ssl' => true,
            'folder' => 'INBOX'
        ];

        $response = $this->postJson('/api/v1/accounts', $accountData);

        $response->assertStatus(201)
                ->assertJson([
                    'success' => true,
                    'message' => 'Account created successfully'
                ])
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'name',
                        'email',
                        'protocol',
                        'server',
                        'port',
                        'username',
                        'owner_id'
                    ]
                ]);

        $this->assertDatabaseHas('accounts', [
            'email' => '<EMAIL>',
            'owner_id' => $userId
        ]);
    }

    /** @test */
    public function account_creation_fails_with_invalid_data()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $testCases = [
            // Missing required fields
            [
                'data' => [],
                'expectedErrors' => ['name', 'email', 'protocol', 'server', 'port', 'username', 'password']
            ],
            // Invalid email
            [
                'data' => [
                    'name' => 'Test',
                    'email' => 'invalid-email',
                    'protocol' => 'imap',
                    'server' => 'test.com',
                    'port' => 993,
                    'username' => 'user',
                    'password' => 'pass'
                ],
                'expectedErrors' => ['email']
            ],
            // Invalid protocol
            [
                'data' => [
                    'name' => 'Test',
                    'email' => '<EMAIL>',
                    'protocol' => 'invalid',
                    'server' => 'test.com',
                    'port' => 993,
                    'username' => 'user',
                    'password' => 'pass'
                ],
                'expectedErrors' => ['protocol']
            ],
            // Invalid port
            [
                'data' => [
                    'name' => 'Test',
                    'email' => '<EMAIL>',
                    'protocol' => 'imap',
                    'server' => 'test.com',
                    'port' => 'not-a-number',
                    'username' => 'user',
                    'password' => 'pass'
                ],
                'expectedErrors' => ['port']
            ]
        ];

        foreach ($testCases as $testCase) {
            $response = $this->postJson('/api/v1/accounts', $testCase['data']);

            $response->assertStatus(422)
                    ->assertJson([
                        'success' => false,
                        'message' => 'Please check your data'
                    ]);

            foreach ($testCase['expectedErrors'] as $field) {
                $response->assertJsonValidationErrors($field);
            }
        }
    }

    /** @test */
    public function account_creation_fails_with_duplicate_email()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        // Create an existing account
        Account::factory()->create(['email' => '<EMAIL>']);

        $accountData = [
            'name' => 'Test Account',
            'email' => '<EMAIL>', // Duplicate email
            'protocol' => 'imap',
            'server' => 'imap.example.com',
            'port' => 993,
            'username' => 'testuser',
            'password' => 'testpass'
        ];

        $response = $this->postJson('/api/v1/accounts', $accountData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    /** @test */
    public function user_can_view_their_own_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);

        $response = $this->getJson("/api/v1/accounts/{$account->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'id' => $account->id,
                        'name' => $account->name,
                        'email' => $account->email
                    ]
                ]);
    }

    /** @test */
    public function user_cannot_view_other_users_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        // Create account owned by another user
        $otherAccount = Account::factory()->create(['owner_id' => 2]);

        $response = $this->getJson("/api/v1/accounts/{$otherAccount->id}");

        $response->assertStatus(403)
                ->assertJson(['error' => 'Forbidden']);
    }

    /** @test */
    public function user_can_update_their_own_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);

        $updateData = [
            'name' => 'Updated Account Name',
            'server' => 'updated.example.com',
            'port' => 995
        ];

        $response = $this->putJson("/api/v1/accounts/{$account->id}", $updateData);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Account updated successfully'
                ]);

        $this->assertDatabaseHas('accounts', [
            'id' => $account->id,
            'name' => 'Updated Account Name',
            'server' => 'updated.example.com',
            'port' => 995
        ]);
    }

    /** @test */
    public function user_cannot_update_other_users_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $otherAccount = Account::factory()->create(['owner_id' => 2]);

        $updateData = ['name' => 'Hacked Account'];

        $response = $this->putJson("/api/v1/accounts/{$otherAccount->id}", $updateData);

        $response->assertStatus(403)
                ->assertJson(['error' => 'Forbidden']);
    }

    /** @test */
    public function user_can_delete_their_own_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);

        $response = $this->deleteJson("/api/v1/accounts/{$account->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Account deleted successfully'
                ]);

        $this->assertSoftDeleted('accounts', ['id' => $account->id]);
    }

    /** @test */
    public function user_cannot_delete_other_users_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $otherAccount = Account::factory()->create(['owner_id' => 2]);

        $response = $this->deleteJson("/api/v1/accounts/{$otherAccount->id}");

        $response->assertStatus(403)
                ->assertJson(['error' => 'Forbidden']);

        $this->assertDatabaseHas('accounts', ['id' => $otherAccount->id]);
    }

    /** @test */
    public function user_can_test_connection_for_their_own_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);

        // Mock successful connection test
        $this->emailClient->shouldReceive('testConnection')
                          ->with($account)
                          ->once()
                          ->andReturn(true);

        $response = $this->getJson("/api/v1/accounts/{$account->id}/test");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Connection successful'
                ]);
    }

    /** @test */
    public function user_cannot_test_connection_for_other_users_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $otherAccount = Account::factory()->create(['owner_id' => 2]);

        $response = $this->getJson("/api/v1/accounts/{$otherAccount->id}/test");

        $response->assertStatus(403)
                ->assertJson(['error' => 'Forbidden']);
    }

    /** @test */
    public function connection_test_returns_failure_when_connection_fails()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);

        // Mock failed connection test
        $this->emailClient->shouldReceive('testConnection')
                          ->with($account)
                          ->once()
                          ->andReturn(false);

        $response = $this->getJson("/api/v1/accounts/{$account->id}/test");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => false,
                    'message' => 'Connection failed'
                ]);
    }

    /** @test */
    public function user_can_check_messages_for_their_own_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);

        // Mock successful message check
        $expectedResult = [
            'success' => true,
            'message' => 'Found 5 new messages out of 10 total across 3 folders',
            'new_messages' => 5,
            'folder_results' => [
                'INBOX' => [
                    'total_messages' => 5,
                    'new_messages' => 3
                ],
                'Sent' => [
                    'total_messages' => 3,
                    'new_messages' => 1
                ],
                'Drafts' => [
                    'total_messages' => 2,
                    'new_messages' => 1
                ]
            ]
        ];

        $this->emailClient->shouldReceive('checkNewMessages')
                          ->with($account)
                          ->once()
                          ->andReturn($expectedResult);

        $response = $this->getJson("/api/v1/accounts/{$account->id}/check");

        $response->assertStatus(200)
                ->assertJson($expectedResult);
    }

    /** @test */
    public function user_cannot_check_messages_for_other_users_account()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $otherAccount = Account::factory()->create(['owner_id' => 2]);

        $response = $this->getJson("/api/v1/accounts/{$otherAccount->id}/check");

        $response->assertStatus(403)
                ->assertJson(['error' => 'Forbidden']);
    }

    /** @test */
    public function account_not_found_returns_404()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $response = $this->getJson('/api/v1/accounts/999999');

        $response->assertStatus(404)
                ->assertJson(['message' => 'Record not found.']);
    }

    /** @test */
    public function update_validation_works_correctly()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        $account = Account::factory()->create(['owner_id' => $userId]);

        $testCases = [
            // Invalid protocol
            [
                'data' => ['protocol' => 'invalid'],
                'expectedErrors' => ['protocol']
            ],
            // Invalid port
            [
                'data' => ['port' => 'not-a-number'],
                'expectedErrors' => ['port']
            ],
            // Invalid boolean
            [
                'data' => ['use_ssl' => 'not-boolean'],
                'expectedErrors' => ['use_ssl']
            ]
        ];

        foreach ($testCases as $testCase) {
            $response = $this->putJson("/api/v1/accounts/{$account->id}", $testCase['data']);

            $response->assertStatus(422)
                    ->assertJson([
                        'success' => false,
                        'message' => 'Please check your data'
                    ]);

            foreach ($testCase['expectedErrors'] as $field) {
                $response->assertJsonValidationErrors($field);
            }
        }
    }

    /** @test */
    public function protocol_specific_validation_works()
    {
        $userId = 1;
        NarAuth::shouldReceive('check')->andReturn(true);
        NarAuth::shouldReceive('id')->andReturn($userId);

        // Test valid protocols
        $validProtocols = ['imap', 'pop3', 'ews'];

        foreach ($validProtocols as $protocol) {
            $accountData = [
                'name' => "Test {$protocol} Account",
                'email' => "test-{$protocol}@example.com",
                'protocol' => $protocol,
                'server' => 'test.example.com',
                'port' => 993,
                'username' => 'testuser',
                'password' => 'testpass'
            ];

            $response = $this->postJson('/api/v1/accounts', $accountData);
            $response->assertStatus(201);
        }
    }
}