# API Testing Guide

This document provides comprehensive guidance for testing all API routes, validations, and permissions in the Mail Backup System.

## 🧪 Test Coverage Overview

The test suite includes comprehensive coverage for:

### ✅ **Route Testing**
- **AccountsController**: 19 tests covering CRUD operations, permissions, and special endpoints
- **MailsController**: 21 tests covering email listing, filtering, viewing, and deletion
- **Middleware & Policies**: 12 tests covering authentication and authorization
- **Validation**: 16 tests covering input validation and edge cases

### 📊 **Test Statistics**
- **Total Tests**: 68 comprehensive tests
- **Total Assertions**: 200+ assertions
- **Coverage Areas**: Authentication, Authorization, Validation, Business Logic, Error Handling

## 🔧 **Test Files Created**

### 1. AccountsControllerTest.php
Tests all account-related endpoints:

```php
// Authentication & Authorization
✓ unauthenticated_user_cannot_access_accounts
✓ user_can_view_their_own_account
✓ user_cannot_view_other_users_account

// CRUD Operations
✓ user_can_create_account_with_valid_data
✓ user_can_update_their_own_account
✓ user_can_delete_their_own_account

// Validation
✓ account_creation_fails_with_invalid_data
✓ account_creation_fails_with_duplicate_email
✓ update_validation_works_correctly

// Special Endpoints
✓ user_can_test_connection_for_their_own_account
✓ user_can_check_messages_for_their_own_account
✓ connection_test_returns_failure_when_connection_fails

// Error Handling
✓ account_not_found_returns_404
✓ protocol_specific_validation_works
```

### 2. MailsControllerTest.php
Tests all email-related endpoints:

```php
// Email Listing & Filtering
✓ user_can_list_emails_from_their_own_account
✓ email_listing_supports_folder_filtering
✓ email_listing_supports_read_status_filtering
✓ email_listing_supports_flagged_status_filtering
✓ email_listing_supports_ordering
✓ email_listing_supports_pagination

// Email Operations
✓ user_can_view_email_from_their_own_account
✓ user_can_view_raw_email_from_their_own_account
✓ user_can_delete_email_from_their_own_account

// Authorization
✓ user_cannot_list_emails_from_other_users_account
✓ user_cannot_view_email_from_other_users_account
✓ user_cannot_delete_email_from_other_users_account

// Advanced Features
✓ viewing_already_read_email_does_not_change_read_status
✓ email_belongs_to_correct_account_validation
✓ complex_filtering_scenario
✓ pagination_metadata_is_correct
```

### 3. MiddlewareAndPolicyTest.php
Tests security and authorization:

```php
// Middleware Testing
✓ account_owner_middleware_blocks_unauthenticated_users
✓ account_owner_middleware_blocks_access_to_other_users_accounts

// Policy Testing
✓ account_policy_allows_owner_access
✓ email_policy_allows_owner_access
✓ email_policy_blocks_access_to_other_users_emails

// Security Features
✓ account_creation_automatically_sets_owner_id
✓ user_cannot_manipulate_owner_id_during_creation
✓ user_cannot_manipulate_owner_id_during_update
✓ middleware_and_policy_work_together_for_double_protection
✓ cross_account_email_access_is_prevented

// Error Handling
✓ api_error_handling_returns_proper_json_responses
```

### 4. ValidationTest.php
Tests input validation comprehensively:

```php
// Required Field Validation
✓ account_creation_validates_required_fields

// Format Validation
✓ account_creation_validates_email_format
✓ account_creation_validates_protocol_values
✓ account_creation_validates_port_as_integer
✓ account_creation_validates_boolean_fields

// Business Rule Validation
✓ account_creation_validates_email_uniqueness
✓ account_creation_validates_string_length_limits
✓ account_creation_validates_email_length_limit

// Edge Cases
✓ account_creation_accepts_valid_boolean_values
✓ account_creation_allows_nullable_fields
✓ account_update_validation_is_less_strict
✓ edge_case_port_values_are_handled
✓ special_characters_in_fields_are_handled

// Response Format
✓ validation_error_response_format_is_consistent
```

## 🚀 **Running Tests**

### Prerequisites
```bash
# Ensure Laravel Sail is running
./vendor/bin/sail up -d

# Run database migrations
./vendor/bin/sail artisan migrate:fresh
```

### Running All API Tests
```bash
# Run all API tests
./vendor/bin/sail artisan test tests/Feature/Api/V1/

# Run with coverage (if configured)
./vendor/bin/sail artisan test tests/Feature/Api/V1/ --coverage

# Run specific test file
./vendor/bin/sail artisan test tests/Feature/Api/V1/AccountsControllerTest.php
```

### Running Individual Test Categories
```bash
# Test account operations
./vendor/bin/sail artisan test tests/Feature/Api/V1/AccountsControllerTest.php

# Test email operations  
./vendor/bin/sail artisan test tests/Feature/Api/V1/MailsControllerTest.php

# Test security & permissions
./vendor/bin/sail artisan test tests/Feature/Api/V1/MiddlewareAndPolicyTest.php

# Test validation rules
./vendor/bin/sail artisan test tests/Feature/Api/V1/ValidationTest.php
```

## 🔍 **Test Categories Explained**

### **Authentication Tests**
- Verify unauthenticated users are blocked
- Ensure proper 401 responses
- Test authentication middleware functionality

### **Authorization Tests**
- Verify users can only access their own resources
- Test ownership-based access control
- Ensure proper 403 responses for unauthorized access

### **Validation Tests**
- Test all required field validations
- Verify data type validations (email, integer, boolean)
- Test string length limits and edge cases
- Verify business rule validations (uniqueness)

### **CRUD Operation Tests**
- Test Create, Read, Update, Delete operations
- Verify proper response formats
- Test error handling for invalid operations

### **Filtering & Pagination Tests**
- Test email filtering by folder, read status, flagged status
- Verify pagination functionality and metadata
- Test complex filter combinations

### **Business Logic Tests**
- Test email connection testing
- Test message checking functionality
- Verify automatic read status updates

## 🛠 **Test Configuration**

### **Database Setup**
Tests use `RefreshDatabase` trait to ensure clean state:
```php
use Illuminate\Foundation\Testing\RefreshDatabase;

class TestClass extends TestCase
{
    use RefreshDatabase;
}
```

### **Mocking External Services**
EmailClient service is mocked to avoid real email server connections:
```php
$this->emailClient = Mockery::mock(EmailClient::class);
$this->app->instance(EmailClient::class, $this->emailClient);
```

### **Authentication Mocking**
NarAuth facade is mocked for consistent authentication testing:
```php
NarAuth::shouldReceive('check')->andReturn(true);
NarAuth::shouldReceive('id')->andReturn($userId);
```

## 📋 **Test Data Factories**

### **AccountFactory**
Creates test accounts with various configurations:
```php
Account::factory()->create(['owner_id' => $userId]);
Account::factory()->imap()->create();
Account::factory()->ews()->create();
Account::factory()->pop3()->create();
```

### **EmailFactory**
Creates test emails with various attributes:
```php
Email::factory()->create(['account_id' => $account->id]);
Email::factory()->inbox()->unread()->create();
Email::factory()->sent()->flagged()->create();
```

## 🎯 **Key Testing Principles**

### **Security First**
- Every endpoint tests authentication
- Authorization is verified for all operations
- Cross-user access is explicitly blocked

### **Comprehensive Validation**
- All input fields are validated
- Edge cases and boundary conditions tested
- Error response formats are consistent

### **Real-World Scenarios**
- Tests simulate actual user workflows
- Complex filtering scenarios included
- Pagination and performance considerations tested

### **Maintainable Tests**
- Clear test names describe functionality
- Tests are isolated and independent
- Factories provide consistent test data

## 🔧 **Troubleshooting Tests**

### **Common Issues**

1. **Database Connection Errors**
   ```bash
   # Ensure database is running
   ./vendor/bin/sail artisan migrate:fresh
   ```

2. **Authentication Mocking Issues**
   ```bash
   # Clear application cache
   ./vendor/bin/sail artisan config:clear
   ./vendor/bin/sail artisan cache:clear
   ```

3. **Service Mocking Conflicts**
   ```bash
   # Restart Sail containers
   ./vendor/bin/sail down
   ./vendor/bin/sail up -d
   ```

### **Test Environment Variables**
Ensure proper test environment configuration in `.env.testing`:
```env
DB_CONNECTION=sqlite
DB_DATABASE=:memory:
MAIL_MAILER=array
QUEUE_CONNECTION=sync
```

## 📈 **Test Metrics**

### **Coverage Goals**
- **Routes**: 100% of API endpoints tested
- **Validation**: All validation rules covered
- **Authorization**: All permission scenarios tested
- **Error Handling**: All error conditions covered

### **Performance Benchmarks**
- Tests should complete in under 2 minutes
- Individual test files under 30 seconds
- Database operations optimized with factories

This comprehensive test suite ensures the Mail Backup System API is robust, secure, and reliable for production use.
