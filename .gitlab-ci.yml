stages:
  - deploy
variables:
  DOCKER_TLS_CERTDIR: ""
  IMAGE_VERSION: ""

deploy_develop:
  stage: deploy
  script:
    - apt-get clean
    - apt-get update
    # Setup SSH deploy keys
    - 'which ssh-agent || ( apt-get install -qq openssh-client )'
    - eval $(ssh-agent -s)
    - ssh-add <(echo "$SSH_PRIVATE_KEY")
    - mkdir -p ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
    - ssh pbadmin@************* "cd ~/narbulut/apps/MailBackupV2 && git checkout develop && git pull origin develop && docker-compose -f docker-compose2.yml exec -T app composer install && docker-compose -f docker-compose2.yml exec -T app php artisan octane:reload && exit"
  only:
    - develop
