<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\AccountsController;
use App\Http\Controllers\Api\V1\MailsController;

Route::prefix('v1')->middleware('account.owner')->group( function () {
    // Account routes - these don't need the account.owner middleware
    Route::apiResource('accounts', AccountsController::class);
    Route::get('accounts/{account}/test', [AccountsController::class, 'test']);
    Route::get('accounts/{account}/check', [AccountsController::class, 'check']);        
    // Mail routes - all require account ownership
    Route::get('mails/{account}', [MailsController::class, 'index']);
    Route::get('mails/{account}/{mail}', [MailsController::class, 'show']);
    Route::get('mails/{account}/{mail}/raw', [MailsController::class, 'raw']);
    Route::delete('mails/{account}/{mail}', [MailsController::class, 'destroy']);
});

Route::any('/need-login', function () {
    return response()->json(['message' => 'Unauthenticated Action'], 401);
})->name('login');
