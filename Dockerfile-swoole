FROM git.narbulut.com:5050/deployer/images:swoole-php-8.3

ADD . /usr/share/nginx/html

WORKDIR /usr/share/nginx/html

RUN apt-get update && apt-get upgrade -y \
    && mkdir -p /etc/apt/keyrings \
    && apt-get install -y gnupg gosu curl ca-certificates zip unzip git supervisor sqlite3 libcap2-bin libpng-dev python3 dnsutils librsvg2-bin fswatch ffmpeg nano  \
    && curl -sS 'https://keyserver.ubuntu.com/pks/lookup?op=get&search=0xb8dc7e53946656efbce4c1dd71daeaab4ad4cab6' | gpg --dearmor | tee /etc/apt/keyrings/ppa_ondrej_php.gpg > /dev/null \
    && echo "deb [signed-by=/etc/apt/keyrings/ppa_ondrej_php.gpg] https://ppa.launchpadcontent.net/ondrej/php/ubuntu noble main" > /etc/apt/sources.list.d/ppa_ondrej_php.list \

RUN apt-get install -y php8.3-cli php8.3-dev \
           php8.3-pgsql php8.3-sqlite3 php8.3-gd \
           php8.3-curl php8.3-mongodb \
           php8.3-imap

RUN apt-get install -y libxml2-dev libc-client-dev libkrb5-dev

RUN docker-php-ext-configure imap --with-kerberos --with-imap-ssl \
        && docker-php-ext-install imap

RUN docker-php-ext-install soap


RUN usermod -a -G www-data root
RUN chgrp -R www-data storage

RUN chown -R www-data:www-data ./storage
RUN chmod -R 0777 ./storage

ENV OCTANE_WORKERS=4
ENV OCTANE_TASK_WORKERS=2

EXPOSE 80

# production alana kada --nodev kaldırıldı.
CMD composer install && cp .env.example .env -u && php artisan octane:start --server=swoole --host=0.0.0.0 --port 80 --workers=$OCTANE_WORKERS --task-workers=$OCTANE_TASK_WORKERS --max-requests=3000
