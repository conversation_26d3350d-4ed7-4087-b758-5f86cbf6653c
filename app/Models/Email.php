<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Email extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'account_id',
        'message_id',
        'uid',
        'subject',
        'body_text',
        'from_email',
        'from_name',
        'to',
        'cc',
        'bcc',
        'attachments',
        'date',
        'is_read',
        'is_flagged',
        'folder',
        'eml_path',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'to' => 'array',
        'cc' => 'array',
        'bcc' => 'array',
        'attachments' => 'array',
        'date' => 'datetime',
        'is_read' => 'boolean',
        'is_flagged' => 'boolean',
    ];

    /**
     * Get the account that owns the email.
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    /**
     * Set body text before save.
     */
    public function setBodyTextAttribute($value)
    {
        $value = strip_tags($value);
        $value = substr($value, 0, 127);
        $this->attributes['body_text'] = trim($value);
    }
}
