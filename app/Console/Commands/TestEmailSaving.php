<?php

namespace App\Console\Commands;

use App\Models\Account;
use App\Services\EmailClient;
use Illuminate\Console\Command;

class TestEmailSaving extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'emails:email-saving {account_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test saving emails as .eml files in S3 storage';

    /**
     * Execute the console command.
     */
    public function handle(EmailClient $emailClient)
    {
        $accountId = $this->argument('account_id');
        
        if ($accountId) {
            $account = Account::find($accountId);
            if (!$account) {
                $this->error("Account with ID {$accountId} not found.");
                return 1;
            }
            
            $accounts = [$account];
        } else {
            $accounts = Account::where('is_active', true)->get();
            if ($accounts->isEmpty()) {
                $this->error('No active accounts found.');
                return 1;
            }
        }
        
        foreach ($accounts as $account) {
            $this->info("Testing email saving for account: {$account->name} ({$account->email})");
            
            try {
                $result = $emailClient->checkNewMessages($account);
                
                $this->info("Result: " . json_encode($result));
                
                // Check if any emails were saved with eml_path
                $emailsWithEml = $account->emails()->whereNotNull('eml_path')->count();
                $this->info("Emails with .eml files: {$emailsWithEml}");
                
                // Get the latest email with eml_path
                $latestEmail = $account->emails()->whereNotNull('eml_path')->latest()->first();
                if ($latestEmail) {
                    $this->info("Latest email with .eml file:");
                    $this->info("  Subject: {$latestEmail->subject}");
                    $this->info("  EML path: {$latestEmail->eml_path}");
                }
            } catch (\Exception $e) {
                $this->error("Error: " . $e->getMessage());
            }
        }
        
        return 0;
    }
}
