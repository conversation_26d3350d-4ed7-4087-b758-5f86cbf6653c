<?php

namespace App\Console\Commands;

use App\Models\Account;
use App\Models\Email;
use App\Services\EmailClient;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class UpdateExistingEmails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'emails:update-existing {account_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update existing emails with .eml files in S3 storage';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $accountId = $this->argument('account_id');
        
        if ($accountId) {
            $account = Account::find($accountId);
            if (!$account) {
                $this->error("Account with ID {$accountId} not found.");
                return 1;
            }
            
            $accounts = [$account];
        } else {
            $accounts = Account::where('is_active', true)->get();
            if ($accounts->isEmpty()) {
                $this->error('No active accounts found.');
                return 1;
            }
        }
        
        foreach ($accounts as $account) {
            $this->info("Updating existing emails for account: {$account->name} ({$account->email})");
            
            $emails = Email::where('account_id', $account->id)
                ->whereNull('eml_path')
                ->get();
            
            $this->info("Found {$emails->count()} emails to update.");
            
            $bar = $this->output->createProgressBar($emails->count());
            $bar->start();
            
            $updated = 0;
            
            foreach ($emails as $email) {
                try {
                    // Generate a unique filename based on account, date, and a random string
                    $date = now()->format('Y-m-d-H-i-s');
                    $random = substr(md5(rand()), 0, 10);
                    $filename = "emails/{$account->id}/{$date}_{$random}.eml";
                    
                    // Generate the .eml content
                    $emlContent = $this->generateEmlContent($email);
                    
                    if ($emlContent) {
                        // Save the .eml file to S3
                        Storage::disk('s3')->put($filename, $emlContent);
                        
                        // Update the email record
                        $email->eml_path = $filename;
                        $email->save();
                        
                        $updated++;
                    }
                } catch (\Exception $e) {
                    Log::error("Error updating email {$email->id}: " . $e->getMessage());
                }
                
                $bar->advance();
            }
            
            $bar->finish();
            $this->newLine();
            
            $this->info("Updated {$updated} emails with .eml files.");
        }
        
        return 0;
    }
    
    /**
     * Generate .eml content for an email.
     *
     * @param Email $email
     * @return string|null
     */
    protected function generateEmlContent(Email $email)
    {
        try {
            // Construct the headers
            $headers = "From: " . ($email->from_name ? "\"" . $email->from_name . "\" " : "") . "<" . $email->from_email . ">\r\n";
            
            // Format the To addresses
            $to = [];
            foreach ($email->to ?? [] as $recipient) {
                if (!empty($recipient['name'])) {
                    $to[] = "\"" . $recipient['name'] . "\" <" . $recipient['email'] . ">";
                } else {
                    $to[] = $recipient['email'];
                }
            }
            $headers .= "To: " . implode(', ', $to) . "\r\n";
            
            // Format the CC addresses
            if (!empty($email->cc)) {
                $cc = [];
                foreach ($email->cc as $recipient) {
                    if (!empty($recipient['name'])) {
                        $cc[] = "\"" . $recipient['name'] . "\" <" . $recipient['email'] . ">";
                    } else {
                        $cc[] = $recipient['email'];
                    }
                }
                $headers .= "Cc: " . implode(', ', $cc) . "\r\n";
            }
            
            // Format the BCC addresses
            if (!empty($email->bcc)) {
                $bcc = [];
                foreach ($email->bcc as $recipient) {
                    if (!empty($recipient['name'])) {
                        $bcc[] = "\"" . $recipient['name'] . "\" <" . $recipient['email'] . ">";
                    } else {
                        $bcc[] = $recipient['email'];
                    }
                }
                $headers .= "Bcc: " . implode(', ', $bcc) . "\r\n";
            }
            
            $headers .= "Subject: " . $email->subject . "\r\n";
            $headers .= "Date: " . $email->date->toRfc2822String() . "\r\n";
            $headers .= "Message-ID: " . ($email->message_id ?? '<' . md5(rand()) . '@example.com>') . "\r\n";
            
            // Determine content type
            $contentType = "text/plain";
            if ($email->body_html) {
                $contentType = "text/html";
            }
            
            $headers .= "Content-Type: {$contentType}; charset=UTF-8\r\n";
            
            $body = $email->body_text ?? $email->body_html ?? '';
            
            // If we have attachments, we need to create a MIME message
            if (!empty($email->attachments)) {
                // This is a simplified approach - in a real implementation,
                // you would need to create a proper MIME message with boundaries
                $boundary = md5(time());
                $headers = "Content-Type: multipart/mixed; boundary=\"{$boundary}\"\r\n" . $headers;
                $content = "--{$boundary}\r\n";
                $content .= "Content-Type: {$contentType}; charset=UTF-8\r\n\r\n";
                $content .= $body . "\r\n\r\n";
                
                foreach ($email->attachments as $attachment) {
                    $content .= "--{$boundary}\r\n";
                    $content .= "Content-Type: " . ($attachment['mime'] ?? 'application/octet-stream') . "\r\n";
                    $content .= "Content-Disposition: attachment; filename=\"" . ($attachment['name'] ?? 'attachment') . "\"\r\n";
                    $content .= "Content-Transfer-Encoding: base64\r\n\r\n";
                    
                    // In a real implementation, you would read the attachment file and base64 encode it
                    // For now, we'll just add a placeholder
                    $content .= "[Attachment content would be here]\r\n\r\n";
                }
                
                $content .= "--{$boundary}--\r\n";
                
                return $headers . "\r\n" . $content;
            }
            
            return $headers . "\r\n\r\n" . $body;
        } catch (\Exception $e) {
            Log::error("Error generating .eml content: " . $e->getMessage());
            return null;
        }
    }
}
