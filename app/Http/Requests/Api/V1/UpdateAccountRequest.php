<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateAccountRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'name' => 'string|max:255',
            'protocol' => 'in:imap,pop3,ews',
            'server' => 'string|max:255',
            'port' => 'integer',
            'username' => 'string|max:255',
            'password' => 'string|max:255',
            'use_ssl' => 'boolean',
            'folder' => 'nullable|string|max:255',
            'domain' => 'nullable|string|max:255',
            'version' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success'   => false,
            'message'   => "Please check your data",
            'data'      => $validator->errors()
        ]));
    }
}
