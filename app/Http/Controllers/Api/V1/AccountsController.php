<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Account;
use App\Services\EmailClient;
use App\Http\Requests\Api\V1\StoreAccountRequest;
use App\Http\Requests\Api\V1\UpdateAccountRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Narbulut\LaravelAuth\Facades\NarAuth;

class AccountsController extends Controller
{
    use AuthorizesRequests;

    /**
     * The email client instance.
     */
    protected $emailClient;

    /**
     * Create a new controller instance.
     */
    public function __construct(EmailClient $emailClient)
    {
        $this->emailClient = $emailClient;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $this->authorize('viewAny', Account::class);
        
        $accounts = Account::where('owner_id', NarAuth::id())->paginate();
        return response()->json($accounts);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAccountRequest $request): JsonResponse
    {
        $this->authorize('create', Account::class);
        
        $data = $request->validated();
        $data['owner_id'] = NarAuth::id();
        
        $account = Account::create($data);
        
        return response()->json([
            'success' => true,
            'message' => 'Account created successfully',
            'data' => $account,
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $account = Account::findOrFail($id);
        $this->authorize('view', $account);
        
        return response()->json([
            'success' => true,
            'data' => $account,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAccountRequest $request, string $id): JsonResponse
    {
        $account = Account::findOrFail($id);
        $this->authorize('update', $account);
        
        $account->update($request->validated());
        
        return response()->json([
            'success' => true,
            'message' => 'Account updated successfully',
            'data' => $account,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        $account = Account::findOrFail($id);
        $this->authorize('delete', $account);
        
        $account->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'Account deleted successfully',
        ]);
    }

    /**
     * Test the connection to the account.
     */
    public function test(string $id): JsonResponse
    {
        $account = Account::findOrFail($id);
        $this->authorize('test', $account);
        
        $result = $this->emailClient->testConnection($account);
        
        return response()->json([
            'success' => $result,
            'message' => $result ? 'Connection successful' : 'Connection failed',
        ]);
    }

    /**
     * Check for new messages in the account.
     */
    public function check(string $id): JsonResponse
    {
        $account = Account::findOrFail($id);
        $this->authorize('check', $account);
        
        $result = $this->emailClient->checkNewMessages($account);

        return response()->json($result);
    }
}
