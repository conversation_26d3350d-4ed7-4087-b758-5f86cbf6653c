<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Account;
use App\Models\Email;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class MailsController extends Controller
{
    use AuthorizesRequests;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, Account $account): JsonResponse
    {
        $this->authorize('viewAny', [Email::class, $account]);
        
        $query = Email::query();
        
        $query->where('account_id', $account->id);
        
        // Filter by folder
        if ($request->has('folder')) {
            $query->where('folder', $request->folder);
        }
        
        // Filter by read status
        if ($request->has('is_read')) {
            $query->where('is_read', $request->is_read);
        }
        
        // Filter by flagged status
        if ($request->has('is_flagged')) {
            $query->where('is_flagged', $request->is_flagged);
        }
        
        // Order by date (newest first by default)
        $query->orderBy('date', $request->order ?? 'desc');
        
        // Paginate results
        $emails = $query->paginate($request->per_page ?? 15);
        
        return response()->json([
            'success' => true,
            'data' => $emails,
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Account $account, string $id): JsonResponse
    {
        $this->authorize('view', $account);
        
        $email = Email::where('account_id', $account->id)->findOrFail($id);
        $this->authorize('view', $email);
        
        // Mark as read if not already
        if (!$email->is_read) {
            $email->is_read = true;
            $email->save();
        }
        
        return response()->json([
            'success' => true,
            'data' => $email,
        ]);
    }

    /**
     * Display the eml source of email, return raw data
     */
    public function raw(Account $account, string $id)
    {
        $this->authorize('view', $account);
        
        $email = Email::where('account_id', $account->id)->findOrFail($id);
        $this->authorize('viewRaw', $email);
        
        $data = Storage::disk('s3')->get($email->eml_path);
        
        return $data;
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Account $account, string $id): JsonResponse
    {
        $this->authorize('view', $account);
        
        $email = Email::where('account_id', $account->id)->findOrFail($id);
        $this->authorize('delete', $email);
        
        $email->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'Email deleted successfully',
        ]);
    }
}
