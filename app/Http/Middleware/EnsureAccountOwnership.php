<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Account;
use Narbulut\LaravelAuth\Facades\NarAuth;

class EnsureAccountOwnership
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!NarAuth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // Get the account from route parameters
        $account = $request->route('account');
        
        if ($account instanceof Account) {
            // Check if the account belongs to the authenticated user
            if ($account->owner_id !== NarAuth::id()) {
                return response()->json(['error' => 'Forbidden'], 403);
            }
        }

        return $next($request);
    }
}
