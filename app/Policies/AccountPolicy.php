<?php

namespace App\Policies;

use App\Models\Account;
use Narbulut\LaravelAuth\Facades\NarAuth;

class AccountPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny($user): bool
    {
        return NarAuth::check();
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view($user, Account $account): bool
    {
        return NarAuth::check() && $account->owner_id === NarAuth::id();
    }

    /**
     * Determine whether the user can create models.
     */
    public function create($user): bool
    {
        return NarAuth::check();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update($user, Account $account): bool
    {
        return NarAuth::check() && $account->owner_id === NarAuth::id();
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete($user, Account $account): bool
    {
        return NarAuth::check() && $account->owner_id === NarAuth::id();
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore($user, Account $account): bool
    {
        return NarAuth::check() && $account->owner_id === NarAuth::id();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete($user, Account $account): bool
    {
        return NarAuth::check() && $account->owner_id === NarAuth::id();
    }

    /**
     * Determine whether the user can test the account connection.
     */
    public function test($user, Account $account): bool
    {
        return NarAuth::check() && $account->owner_id === NarAuth::id();
    }

    /**
     * Determine whether the user can check for new messages.
     */
    public function check($user, Account $account): bool
    {
        return NarAuth::check() && $account->owner_id === NarAuth::id();
    }
}
