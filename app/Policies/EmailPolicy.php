<?php

namespace App\Policies;

use App\Models\Email;
use App\Models\Account;
use Narbulut\LaravelAuth\Facades\NarAuth;

class EmailPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny($user, Account $account): bool
    {
        return NarAuth::check() && $account->owner_id === NarAuth::id();
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view($user, Email $email): bool
    {
        return NarAuth::check() && $email->account->owner_id === NarAuth::id();
    }

    /**
     * Determine whether the user can create models.
     */
    public function create($user, Account $account): bool
    {
        return NarAuth::check() && $account->owner_id === NarAuth::id();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update($user, Email $email): bool
    {
        return NarAuth::check() && $email->account->owner_id === NarAuth::id();
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete($user, Email $email): bool
    {
        return NarAuth::check() && $email->account->owner_id === NarAuth::id();
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore($user, Email $email): bool
    {
        return NarAuth::check() && $email->account->owner_id === NarAuth::id();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete($user, Email $email): bool
    {
        return NarAuth::check() && $email->account->owner_id === NarAuth::id();
    }

    /**
     * Determine whether the user can view the raw email content.
     */
    public function viewRaw($user, Email $email): bool
    {
        return NarAuth::check() && $email->account->owner_id === NarAuth::id();
    }
}
