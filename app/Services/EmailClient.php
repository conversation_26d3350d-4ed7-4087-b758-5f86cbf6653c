<?php

namespace App\Services;

use App\Models\Account;
use App\Models\Email;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use PhpImap\Exceptions\ConnectionException;
use PhpImap\Mailbox;
use jamesiarmes\PhpEws\Client as EwsClient;
use jamesiarmes\PhpEws\Request\FindItemType;
use jamesiarmes\PhpEws\Request\GetItemType;
use jamesiarmes\PhpEws\Request\FindFolderType;
use jamesiarmes\PhpEws\ArrayType\NonEmptyArrayOfBaseFolderIdsType;
use jamesiarmes\PhpEws\Enumeration\DefaultShapeNamesType;
use jamesiarmes\PhpEws\Enumeration\DistinguishedFolderIdNameType;
use jamesiarmes\PhpEws\Enumeration\ResponseClassType;
use jamesiarmes\PhpEws\Enumeration\FolderQueryTraversalType;
use jamesiarmes\PhpEws\Type\DistinguishedFolderIdType;
use jamesiarmes\PhpEws\Type\ItemResponseShapeType;
use jamesiarmes\PhpEws\Type\PathToUnindexedFieldType;
use jamesiarmes\PhpEws\Type\FolderResponseShapeType;
use jamesiarmes\PhpEws\Type\FolderIdType;
use jamesiarmes\PhpEws\Type\RestrictionType;
use jamesiarmes\PhpEws\Type\IsGreaterThanType;
use jamesiarmes\PhpEws\Type\IsEqualToType;
use jamesiarmes\PhpEws\Type\FieldURIOrConstantType;
use jamesiarmes\PhpEws\Type\ConstantValueType;
use jamesiarmes\PhpEws\Enumeration\UnindexedFieldURIType;

class EmailClient
{
    /**
     * Create a new EmailClient instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the appropriate client for the account.
     *
     * @param Account $account
     * @return mixed
     * @throws Exception
     */
    public function getClient(Account $account)
    {
        switch ($account->protocol) {
            case 'imap':
                return $this->getImapClient($account);
            case 'pop3':
                return $this->getPopClient($account);
            case 'ews':
                return $this->getEwsClient($account);
            default:
                throw new Exception("Unsupported protocol: {$account->protocol}");
        }
    }

    /**
     * Get an IMAP client for the account.
     *
     * @param Account $account
     * @param string|null $folder Specific folder to connect to (optional)
     * @return Mailbox
     */
    protected function getImapClient(Account $account, $folder = null)
    {
        $protocol = $account->use_ssl ? 'imap/ssl' : 'imap';

        // If no specific folder is requested, don't specify a folder for folder discovery
        // If a folder is specified, use it; otherwise use the account's default folder or INBOX
        $folderPath = '';
        if ($folder !== null) {
            $folderPath = $folder;
        } elseif (isset($account->folder)) {
            $folderPath = $account->folder;
        }

        $mailbox = new Mailbox(
            "{{$account->server}:{$account->port}/{$protocol}}{$folderPath}",
            $account->username,
            $account->password,
            '../storage/attachments',
            'UTF-8'
        );

        return $mailbox;
    }

    /**
     * Get a POP3 client for the account.
     *
     * @param Account $account
     * @return Mailbox
     */
    protected function getPopClient(Account $account)
    {
        $protocol = $account->use_ssl ? 'pop3/ssl' : 'pop3';
        $mailbox = new Mailbox(
            "{{$account->server}:{$account->port}/{$protocol}}",
            $account->username,
            $account->password,
            '../storage/attachments',
            'UTF-8'
        );

        return $mailbox;
    }

    /**
     * Get an EWS client for the account.
     *
     * @param Account $account
     * @return EwsClient
     */
    protected function getEwsClient(Account $account)
    {
        // Determine the server URL based on the server name
        $server = $account->server;

        // Special handling for Office 365
        if (str_contains($server, 'outlook.office.com') || str_contains($server, 'outlook.office365.com')) {
            $server = 'https://outlook.office365.com/EWS/Exchange.asmx';
        }

        // For Exchange servers, the username often needs to be in the format 'domain\username' or 'username@domain'
        $username = $account->username;

        // If the username doesn't contain @ or \, and domain is specified, format it appropriately
        if ($account->domain && !str_contains($username, '\\') && !str_contains($username, '@')) {
            // Try domain\username format
            $username = $account->domain . '\\' . $username;
        }

        // For Office 365, ensure the username is in email format
        if ((str_contains($server, 'outlook.office.com') || str_contains($server, 'outlook.office365.com')) && !str_contains($username, '@')) {
            // Try to extract the domain from the email address
            if ($account->domain) {
                $username = $username . '@' . $account->domain;
            }
        }

        // Create the client with server URL and credentials
        $client = new EwsClient($server, $username, $account->password);

        // Set the version if specified (e.g., Exchange2010, Exchange2013, Exchange2016)
        if ($account->version) {
            $client->setVersion($account->version);
        } else {
            // Default to Exchange2016 for Office 365, Exchange2013 for others
            if (str_contains($server, 'outlook.office.com') || str_contains($server, 'outlook.office365.com')) {
                $client->setVersion(EwsClient::VERSION_2016);
            } else {
                $client->setVersion(EwsClient::VERSION_2013);
            }
        }

        // Log connection details (without password)
        Log::info("EWS: Setting up connection", [
            'server' => $server,
            'username' => $username,
            'version' => $account->version ?: ($server === 'https://outlook.office365.com/EWS/Exchange.asmx' ? 'Exchange2016 (default)' : 'Exchange2013 (default)'),
            'domain' => $account->domain,
        ]);

        return $client;
    }

    /**
     * Test the connection to the account.
     *
     * @param Account $account
     * @return bool
     */
    public function testConnection(Account $account)
    {
        try {
            switch ($account->protocol) {
                case 'imap':
                case 'pop3':
                    $client = $this->getClient($account);
                    $client->checkMailbox();
                    break;
                case 'ews':
                    // For EWS, we'll try different authentication methods
                    return $this->testEwsConnection($account);
                default:
                    throw new Exception("Unsupported protocol: {$account->protocol}");
            }

            return true;
        } catch (Exception $e) {
            Log::error('Email connection test failed: ' . $e->getMessage(), [
                'account_id' => $account->id,
                'exception' => $e,
            ]);

            return false;
        }
    }

    /**
     * Test the connection to an EWS account by trying different authentication methods.
     *
     * @param Account $account
     * @return bool
     */
    protected function testEwsConnection(Account $account)
    {
        // Try with the standard configuration first
        try {
            $client = $this->getEwsClient($account);

            // For EWS, we'll try to get the inbox folder
            $request = new FindItemType();
            $request->ParentFolderIds = new NonEmptyArrayOfBaseFolderIdsType();

            $folderId = new DistinguishedFolderIdType();
            $folderId->Id = DistinguishedFolderIdNameType::INBOX;
            $request->ParentFolderIds->DistinguishedFolderId[] = $folderId;

            $response = $client->FindItem($request);

            if ($response->ResponseMessages->FindItemResponseMessage[0]->ResponseClass !== ResponseClassType::SUCCESS) {
                throw new Exception($response->ResponseMessages->FindItemResponseMessage[0]->MessageText);
            }

            return true;
        } catch (Exception $e) {
            Log::warning('EWS: Standard connection failed: ' . $e->getMessage(), [
                'account_id' => $account->id,
                'exception' => $e,
            ]);

            // If the standard connection failed, try with different username formats
            $alternativeUsernames = [];

            // Original username
            $alternativeUsernames[] = $account->username;

            // If username contains @, try without domain
            if (str_contains($account->username, '@')) {
                $parts = explode('@', $account->username);
                $alternativeUsernames[] = $parts[0];
            }

            // If domain is specified, try domain\username format
            if ($account->domain && !str_contains($account->username, '\\')) {
                $alternativeUsernames[] = $account->domain . '\\' . $account->username;
            }

            // If username doesn't contain @, try username@domain format
            if ($account->domain && !str_contains($account->username, '@')) {
                $alternativeUsernames[] = $account->username . '@' . $account->domain;
            }

            // Try different Exchange versions
            $exchangeVersions = [
                EwsClient::VERSION_2007,
                EwsClient::VERSION_2007_SP1,
                EwsClient::VERSION_2009,
                EwsClient::VERSION_2010,
                EwsClient::VERSION_2010_SP1,
                EwsClient::VERSION_2010_SP2,
                EwsClient::VERSION_2013,
                EwsClient::VERSION_2013_SP1,
                EwsClient::VERSION_2016
            ];

            // Try each alternative username
            foreach ($alternativeUsernames as $username) {
                // Try each Exchange version
                foreach ($exchangeVersions as $version) {
                    try {
                        Log::info("EWS: Trying alternative username: {$username} with version: {$version}");

                        // Create a new client with the alternative username
                        $client = new EwsClient($account->server, $username, $account->password);
                        $client->setVersion($version);

                        // Try to get the inbox folder
                        $request = new FindItemType();
                        $request->ParentFolderIds = new NonEmptyArrayOfBaseFolderIdsType();

                        $folderId = new DistinguishedFolderIdType();
                        $folderId->Id = DistinguishedFolderIdNameType::INBOX;
                        $request->ParentFolderIds->DistinguishedFolderId[] = $folderId;

                        $response = $client->FindItem($request);

                        if ($response->ResponseMessages->FindItemResponseMessage[0]->ResponseClass !== ResponseClassType::SUCCESS) {
                            throw new Exception($response->ResponseMessages->FindItemResponseMessage[0]->MessageText);
                        }

                        // If we get here, the connection was successful
                        Log::info("EWS: Connection successful with username: {$username} and version: {$version}");

                        // Update the account with the successful username format and version
                        if ($username !== $account->username || $version !== $account->version) {
                            $account->username = $username;
                            $account->version = $version;
                            $account->save();
                            Log::info("EWS: Updated account username to: {$username} and version to: {$version}");
                        }

                        return true;
                    } catch (Exception $altE) {
                        Log::warning("EWS: Connection failed with username {$username} and version {$version}: " . $altE->getMessage());
                        // Continue to the next alternative username or version
                    }
                }
            }

            // If all alternatives failed, return false
            return false;
        }
    }

    /**
     * Get all IMAP folders for the account.
     *
     * @param Account $account
     * @return array
     */
    protected function getImapFolders(Account $account)
    {
        // Create mailbox connection without specifying a folder for folder discovery
        $mailbox = $this->getImapClient($account, '');

        try {
            // Get list of all folders using wildcard pattern
            $folders = $mailbox->getMailboxes('*');
            $folderNames = [];

            Log::info("IMAP: Raw folder response for account {$account->id}: " . print_r($folders, true));

            foreach ($folders as $folder) {
                $folderName = '';

                // Try different ways to extract folder name
                if (isset($folder['name']) && !empty($folder['name'])) {
                    $folderName = $folder['name'];
                } elseif (isset($folder['fullpath']) && !empty($folder['fullpath'])) {
                    // Extract folder name from fullpath
                    $fullpath = $folder['fullpath'];

                    // Remove the server part and extract just the folder name
                    if (preg_match('/\{[^}]+\}(.*)/', $fullpath, $matches)) {
                        $folderName = $matches[1];
                    } else {
                        // Fallback: try to extract after the last }
                        $pos = strrpos($fullpath, '}');
                        if ($pos !== false) {
                            $folderName = substr($fullpath, $pos + 1);
                        } else {
                            $folderName = $fullpath;
                        }
                    }
                } elseif (is_string($folder)) {
                    // Sometimes folders are returned as simple strings
                    $folderName = $folder;
                }

                // Clean up folder name
                $folderName = trim($folderName, " \t\n\r\0\x0B/");

                // Skip empty folder names and system folders that shouldn't be processed
                if (!empty($folderName) && !in_array($folderName, ['', '.', '..'])) {
                    $folderNames[] = $folderName;
                }
            }

            // Remove duplicates and ensure INBOX is included
            $folderNames = array_unique($folderNames);

            // Always ensure INBOX is in the list if not already present
            if (!in_array('INBOX', $folderNames)) {
                array_unshift($folderNames, 'INBOX');
            }

            Log::info("IMAP: Found " . count($folderNames) . " folders for account {$account->id}: " . implode(', ', $folderNames));

            return $folderNames;
        } catch (Exception $e) {
            Log::warning("IMAP: Error getting folders for account {$account->id}: " . $e->getMessage());
            Log::warning("IMAP: Exception details: " . $e->getTraceAsString());
        } finally {
            if (isset($mailbox)) {
                try {
                    $mailbox->disconnect();
                } catch (Exception $disconnectE) {
                    Log::warning("IMAP: Error disconnecting mailbox: " . $disconnectE->getMessage());
                }
            }
        }

        // Final fallback to INBOX only
        Log::info("IMAP: Using fallback folder list (INBOX only) for account {$account->id}");
        return ['INBOX'];

    }

    /**
     * Get all EWS email folders for the account.
     *
     * @param Account $account
     * @return array
     */
    protected function getEwsFolders(Account $account)
    {
        try {
            $client = $this->getEwsClient($account);

            // Start with common email folders
            $emailFolders = [
                ['name' => 'INBOX', 'id' => DistinguishedFolderIdNameType::INBOX, 'changeKey' => null],
                ['name' => 'Sent Items', 'id' => DistinguishedFolderIdNameType::SENT, 'changeKey' => null],
                ['name' => 'Drafts', 'id' => DistinguishedFolderIdNameType::DRAFTS, 'changeKey' => null],
                ['name' => 'Deleted Items', 'id' => DistinguishedFolderIdNameType::DELETED, 'changeKey' => null],
                ['name' => 'Junk Email', 'id' => DistinguishedFolderIdNameType::JUNK, 'changeKey' => null],
            ];

            // Create the request to find folders under the mail folder (msgfolderroot)
            $request = new FindFolderType();
            $request->Traversal = FolderQueryTraversalType::DEEP;

            // Set the parent folder to search from (msgfolderroot - mail folders only)
            $request->ParentFolderIds = new NonEmptyArrayOfBaseFolderIdsType();
            $folderId = new DistinguishedFolderIdType();
            $folderId->Id = DistinguishedFolderIdNameType::MESSAGE_ROOT;
            $request->ParentFolderIds->DistinguishedFolderId[] = $folderId;

            // Add restriction to only get mail folders (IPF.Note folder class)
            $restriction = new RestrictionType();
            $restriction->IsEqualTo = new IsEqualToType();
            $restriction->IsEqualTo->FieldURI = new PathToUnindexedFieldType();
            $restriction->IsEqualTo->FieldURI->FieldURI = UnindexedFieldURIType::FOLDER_FOLDER_CLASS;
            $restriction->IsEqualTo->FieldURIOrConstant = new FieldURIOrConstantType();
            $restriction->IsEqualTo->FieldURIOrConstant->Constant = new ConstantValueType();
            $restriction->IsEqualTo->FieldURIOrConstant->Constant->Value = 'IPF.Note';
            $request->Restriction = $restriction;

            // Set the folder shape
            $folderShape = new FolderResponseShapeType();
            $folderShape->BaseShape = DefaultShapeNamesType::DEFAULT_PROPERTIES;
            $request->FolderShape = $folderShape;

            Log::info("EWS: Getting email folders for account {$account->id}");
            $response = $client->FindFolder($request);

            if ($response->ResponseMessages->FindFolderResponseMessage[0]->ResponseClass !== ResponseClassType::SUCCESS) {
                Log::warning("EWS: Error finding custom folders: " . $response->ResponseMessages->FindFolderResponseMessage[0]->MessageText);
                // Return just the standard folders if we can't get custom ones
                Log::info("EWS: Using standard email folders only for account {$account->id}");
                return $emailFolders;
            }

            $folderItems = $response->ResponseMessages->FindFolderResponseMessage[0]->RootFolder->Folders->Folder ?? [];

            // Add custom email folders to our list
            foreach ($folderItems as $folder) {
                // Skip if this folder name already exists in our standard folders
                $folderName = $folder->DisplayName;
                $exists = false;
                foreach ($emailFolders as $existingFolder) {
                    if ($existingFolder['name'] === $folderName) {
                        $exists = true;
                        break;
                    }
                }

                if (!$exists) {
                    $emailFolders[] = [
                        'name' => $folderName,
                        'id' => $folder->FolderId->Id,
                        'changeKey' => $folder->FolderId->ChangeKey
                    ];
                }
            }

            Log::info("EWS: Found " . count($emailFolders) . " email folders for account {$account->id}: " .
                     implode(', ', array_column($emailFolders, 'name')));

            return $emailFolders;
        } catch (Exception $e) {
            Log::warning("EWS: Error getting folders for account {$account->id}: " . $e->getMessage());
            // Fallback to standard email folders only
            return [
                ['name' => 'INBOX', 'id' => DistinguishedFolderIdNameType::INBOX, 'changeKey' => null],
                ['name' => 'Sent Items', 'id' => DistinguishedFolderIdNameType::SENT, 'changeKey' => null],
                ['name' => 'Drafts', 'id' => DistinguishedFolderIdNameType::DRAFTS, 'changeKey' => null],
            ];
        }
    }

    /**
     * Check for new messages in the account.
     *
     * @param Account $account
     * @return array
     */
    public function checkNewMessages(Account $account)
    {
        try {
            switch ($account->protocol) {
                case 'imap':
                    return $this->checkNewImapMessages($account);
                case 'pop3':
                    return $this->checkNewPopMessages($account);
                case 'ews':
                    return $this->checkNewEwsMessages($account);
                default:
                    throw new Exception("Unsupported protocol: {$account->protocol}");
            }
        } catch (Exception $e) {
            Log::error('Failed to check new messages: ' . $e->getMessage(), [
                'account_id' => $account->id,
                'exception' => $e,
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'new_messages' => 0,
            ];
        } finally {
            // Update the last checked timestamp
            $account->last_checked_at = now();
            $account->save();
        }
    }

    /**
     * Check for new IMAP messages.
     *
     * @param Account $account
     * @return array
     */
    protected function checkNewImapMessages(Account $account)
    {
        // Get all folders for this account
        $folders = $this->getImapFolders($account);

        $totalNewMessages = 0;
        $totalProcessedMessages = 0;
        $folderResults = [];

        // Get existing UIDs and message IDs for this account
        $existingUids = Email::where('account_id', $account->id)
            ->whereNotNull('uid')
            ->pluck('uid')
            ->toArray();

        $existingMessageIds = Email::where('account_id', $account->id)
            ->whereNotNull('message_id')
            ->pluck('message_id')
            ->toArray();

        // Determine the date filter for new messages
        $lastChecked = $account->last_checked_at;
        $searchCriteria = 'ALL';
        if ($lastChecked) {
            // Search for messages since last check (IMAP date format: DD-MMM-YYYY)
            $searchCriteria = 'SINCE ' . $lastChecked->format('d-M-Y');
            Log::info("IMAP: Using date filter for account {$account->id}: {$searchCriteria}");
        }

        foreach ($folders as $folderName) {
            try {
                Log::info("IMAP: Checking folder '{$folderName}' for account {$account->id}");

                // Create a new mailbox connection for this folder using the updated method
                $mailbox = $this->getImapClient($account, $folderName);

                $mailbox->checkMailbox();

                // Search for messages - use date filter if available, otherwise get all
                $mailIds = $mailbox->searchMailbox($searchCriteria);

                Log::info("IMAP: Found " . count($mailIds) . " messages in folder '{$folderName}' for account {$account->id}");

                $newMailIds = [];
                $processedUids = []; // Keep track of UIDs we've already processed

                foreach ($mailIds as $mailId) {
                    try {
                        // Get the UID and message ID using the mail ID
                        $header = $mailbox->getMailHeader($mailId);
                        $uid = $header->uid ?? $mailId;
                        $messageId = $header->messageId ?? null;

                        // Skip if we've already processed this UID in this session
                        if (in_array($uid, $processedUids)) {
                            Log::info("IMAP: Skipping message {$mailId} (already processed in this session) for account {$account->id}");
                            continue;
                        }

                        // Add to processed UIDs
                        $processedUids[] = $uid;

                        // Skip if we already have this UID or message ID in the database
                        if (in_array($uid, $existingUids) || ($messageId && in_array($messageId, $existingMessageIds))) {
                            Log::info("IMAP: Skipping message {$mailId} (already exists) for account {$account->id}");
                            continue;
                        }

                        $newMailIds[] = $mailId;
                    } catch (Exception $e) {
                        Log::warning("IMAP: Error getting header for message {$mailId}: " . $e->getMessage());
                    }
                }

                $folderTotalMessages = count($newMailIds);
                Log::info("IMAP: Found " . $folderTotalMessages . " new messages to process in folder '{$folderName}' for account {$account->id}");

                $folderNewMessages = 0;

                foreach ($newMailIds as $mailId) {
                    try {
                        Log::info("IMAP: Processing message {$mailId} in folder '{$folderName}' for account {$account->id}");
                        $mail = $mailbox->getMail($mailId);
                        // Get the UID using the mail ID
                        $header = $mailbox->getMailHeader($mailId);
                        $uid = $header->uid ?? $mailId;

                        Log::info("IMAP: Saving new message {$mailId} with subject '{$mail->subject}' in folder '{$folderName}' for account {$account->id}");

                        // Check if this message ID already exists but doesn't have an eml_path
                        $existingEmail = null;
                        if ($mail->messageId) {
                            $existingEmail = Email::where('account_id', $account->id)
                                ->where('message_id', $mail->messageId)
                                ->first();
                        }

                        // If the email exists and already has an eml_path, skip saving a new .eml file
                        $emlPath = null;
                        if (!$existingEmail || !$existingEmail->eml_path) {
                            // Save the email as an .eml file in S3 storage
                            $emlPath = $this->saveEmailAsEml($account, $mail, 'IMAP');
                        } else {
                            $emlPath = $existingEmail->eml_path;
                            Log::info("IMAP: Using existing .eml file: {$emlPath}");
                        }

                        if (!$existingEmail) {
                            // Create a new email record
                            Email::create([
                                'account_id' => $account->id,
                                'message_id' => $mail->messageId,
                                'uid' => $uid,
                                'subject' => $mail->subject ?? '(No Subject)',
                                'body_text' => $mail->textPlain ?? null,
                                'from_email' => $mail->fromAddress,
                                'from_name' => $mail->fromName,
                                'to' => $this->formatAddresses($mail->to),
                                'cc' => $this->formatAddresses($mail->cc),
                                'bcc' => $this->formatAddresses($mail->bcc),
                                'attachments' => $this->formatAttachments($mail->getAttachments()),
                                'date' => Carbon::parse($mail->date),
                                'folder' => $folderName,
                                'eml_path' => $emlPath,
                            ]);
                            Log::info("IMAP: Created new email record for message {$mailId} in folder '{$folderName}'");
                        }

                        $folderNewMessages++;
                        $totalNewMessages++;
                        Log::info("IMAP: Successfully saved message {$mailId} in folder '{$folderName}' for account {$account->id}");
                    } catch (Exception $e) {
                        // Log the error but continue processing other messages
                        Log::warning("IMAP: Error processing message {$mailId} in folder '{$folderName}': " . $e->getMessage(), [
                            'account_id' => $account->id,
                            'exception' => $e,
                        ]);
                    }
                }

                $totalProcessedMessages += $folderTotalMessages;
                $folderResults[$folderName] = [
                    'total_messages' => $folderTotalMessages,
                    'new_messages' => $folderNewMessages,
                ];

                Log::info("IMAP: Completed checking folder '{$folderName}'. Found {$folderNewMessages} new messages out of {$folderTotalMessages} total for account {$account->id}");

            } catch (Exception $e) {
                Log::error("IMAP: Error checking folder '{$folderName}' for account {$account->id}: " . $e->getMessage(), [
                    'account_id' => $account->id,
                    'folder' => $folderName,
                    'exception' => $e,
                ]);
                $folderResults[$folderName] = [
                    'total_messages' => 0,
                    'new_messages' => 0,
                    'error' => $e->getMessage(),
                ];
            } finally {
                // Close the mailbox connection for this folder
                if (isset($mailbox)) {
                    $mailbox->disconnect();
                }
            }
        }

        Log::info("IMAP: Completed checking all folders. Found {$totalNewMessages} new messages out of {$totalProcessedMessages} total for account {$account->id}");

        return [
            'success' => true,
            'message' => "Found {$totalNewMessages} new messages out of {$totalProcessedMessages} total across " . count($folders) . " folders",
            'new_messages' => $totalNewMessages,
            'folder_results' => $folderResults,
        ];
    }

    /**
     * Check for new POP3 messages.
     *
     * @param Account $account
     * @return array
     */
    protected function checkNewPopMessages(Account $account)
    {
        $mailbox = $this->getPopClient($account);

        try {
            Log::info("POP3: Checking mailbox for account {$account->id}");
            $mailbox->checkMailbox();

            // Get existing message IDs for this account
            $existingMessageIds = Email::where('account_id', $account->id)
                ->whereNotNull('message_id')
                ->pluck('message_id')
                ->toArray();

            Log::info("POP3: Found " . count($existingMessageIds) . " existing message IDs for account {$account->id}");

            // Determine the date filter for new messages
            $lastChecked = $account->last_checked_at;
            Log::info("POP3: Last checked at: " . ($lastChecked ? $lastChecked->toIso8601String() : 'never') . " for account {$account->id}");

            // Get all message numbers
            try {
                $messageNumbers = $mailbox->searchMailbox('ALL');
                $totalMessages = count($messageNumbers);
                Log::info("POP3: Found {$totalMessages} total messages in mailbox for account {$account->id}");
            } catch (Exception $e) {
                Log::error("POP3: Error searching mailbox: " . $e->getMessage(), [
                    'account_id' => $account->id,
                    'exception' => $e,
                ]);
                throw $e;
            }

            $newMessages = 0;
            $processedMessageIds = []; // Keep track of message IDs we've already processed
            $candidateMessages = []; // Messages that pass initial filtering

            // First pass: Filter messages by date and existing status
            foreach ($messageNumbers as $mailId) {
                try {
                    Log::info("POP3: Checking message {$mailId} for account {$account->id}");

                    // Try to get the message ID and date from the header first
                    try {
                        $header = $mailbox->getMailHeader($mailId);
                        $messageId = $header->messageId ?? null;
                        $messageDate = isset($header->date) ? Carbon::parse($header->date) : null;

                        // Skip if we've already processed this message ID in this session
                        if ($messageId && in_array($messageId, $processedMessageIds)) {
                            Log::info("POP3: Skipping message {$mailId} (already processed in this session) for account {$account->id}");
                            continue;
                        }

                        // Skip if we already have this message ID in the database
                        if ($messageId && in_array($messageId, $existingMessageIds)) {
                            Log::info("POP3: Skipping message {$mailId} (already exists in database) for account {$account->id}");
                            continue;
                        }

                        // Apply date filter if we have a last checked date
                        if ($lastChecked && $messageDate) {
                            if ($messageDate->lte($lastChecked)) {
                                Log::info("POP3: Skipping message {$mailId} (older than last check: {$messageDate->toIso8601String()} <= {$lastChecked->toIso8601String()}) for account {$account->id}");
                                continue;
                            }
                        }

                        // Add to processed message IDs to avoid duplicates
                        if ($messageId) {
                            $processedMessageIds[] = $messageId;
                        }

                        // This message is a candidate for processing
                        $candidateMessages[] = $mailId;
                        Log::info("POP3: Message {$mailId} is a candidate for processing (date: " . ($messageDate ? $messageDate->toIso8601String() : 'unknown') . ") for account {$account->id}");

                    } catch (Exception $headerEx) {
                        Log::warning("POP3: Error getting header for message {$mailId}: " . $headerEx->getMessage());
                        // If we can't get the header, we'll include it as a candidate and check it fully later
                        $candidateMessages[] = $mailId;
                    }
                } catch (Exception $e) {
                    Log::warning("POP3: Error checking message {$mailId}: " . $e->getMessage(), [
                        'account_id' => $account->id,
                        'exception' => $e,
                    ]);
                }
            }

            Log::info("POP3: Found " . count($candidateMessages) . " candidate messages to process out of {$totalMessages} total for account {$account->id}");

            // Second pass: Process candidate messages
            foreach ($candidateMessages as $mailId) {
                try {
                    $mail = $mailbox->getMail($mailId);

                    // Double-check date filtering for messages where we couldn't get header date
                    if ($lastChecked && $mail->date) {
                        $messageDate = Carbon::parse($mail->date);
                        if ($messageDate->lte($lastChecked)) {
                            Log::info("POP3: Skipping message {$mailId} after full download (older than last check: {$messageDate->toIso8601String()} <= {$lastChecked->toIso8601String()}) for account {$account->id}");
                            continue;
                        }
                    }

                    // Final check - skip if we already have this message ID in the database
                    if ($mail->messageId && in_array($mail->messageId, $existingMessageIds)) {
                        Log::info("POP3: Skipping message {$mailId} (already exists in database after full download) for account {$account->id}");
                        continue;
                    }

                    Log::info("POP3: Saving new message {$mailId} with subject '{$mail->subject}' for account {$account->id}");

                    // Check if this message ID already exists but doesn't have an eml_path
                    $existingEmail = null;
                    if ($mail->messageId) {
                        $existingEmail = Email::where('account_id', $account->id)
                            ->where('message_id', $mail->messageId)
                            ->first();
                    }

                    // If the email exists and already has an eml_path, skip saving a new .eml file
                    $emlPath = null;
                    if (!$existingEmail || !$existingEmail->eml_path) {
                        // Save the email as an .eml file in S3 storage
                        $emlPath = $this->saveEmailAsEml($account, $mail, 'POP3');
                    } else {
                        $emlPath = $existingEmail->eml_path;
                        Log::info("POP3: Using existing .eml file: {$emlPath}");
                    }

                    if (!$existingEmail) {
                        // Create a new email record
                        Email::create([
                            'account_id' => $account->id,
                            'message_id' => $mail->messageId,
                            'uid' => $mailId, // POP3 doesn't have UIDs, so we use the mail ID
                            'subject' => $mail->subject ?? '(No Subject)',
                            'body_text' => $mail->textPlain ?? null,
                            'from_email' => $mail->fromAddress,
                            'from_name' => $mail->fromName,
                            'to' => $this->formatAddresses($mail->to),
                            'cc' => $this->formatAddresses($mail->cc),
                            'bcc' => $this->formatAddresses($mail->bcc),
                            'attachments' => $this->formatAttachments($mail->getAttachments()),
                            'date' => Carbon::parse($mail->date),
                            'folder' => 'INBOX', // POP3 only has INBOX
                            'eml_path' => $emlPath,
                        ]);
                        Log::info("POP3: Created new email record for message {$mailId}");
                    }

                    $newMessages++;
                    Log::info("POP3: Successfully saved message {$mailId} for account {$account->id}");
                } catch (Exception $e) {
                    // Log the error but continue processing other messages
                    Log::warning("POP3: Error processing message {$mailId}: " . $e->getMessage(), [
                        'account_id' => $account->id,
                        'exception' => $e,
                    ]);
                }
            }

            Log::info("POP3: Completed checking for new messages. Found {$newMessages} new messages out of " . count($candidateMessages) . " candidates from {$totalMessages} total for account {$account->id}");

            // POP3 only has INBOX folder, so we create a simple folder result structure
            $folderResults = [
                'INBOX' => [
                    'total_messages' => count($candidateMessages),
                    'new_messages' => $newMessages,
                ],
            ];

            return [
                'success' => true,
                'message' => "Found {$newMessages} new messages out of " . count($candidateMessages) . " candidates from {$totalMessages} total (POP3 - INBOX only)",
                'new_messages' => $newMessages,
                'folder_results' => $folderResults,
            ];
        } catch (ConnectionException $e) {
            Log::error("POP3: Connection error: " . $e->getMessage(), [
                'account_id' => $account->id,
                'exception' => $e,
            ]);
            throw new Exception("POP3 connection error: {$e->getMessage()}");
        } catch (Exception $e) {
            Log::error("POP3: General error: " . $e->getMessage(), [
                'account_id' => $account->id,
                'exception' => $e,
            ]);
            throw $e;
        } finally {
            // Close the mailbox connection
            if (isset($mailbox)) {
                $mailbox->disconnect();
            }
        }
    }

    /**
     * Check for new EWS messages.
     *
     * @param Account $account
     * @return array
     */
    protected function checkNewEwsMessages(Account $account)
    {
        try {
            Log::info("EWS: Checking mailbox for account {$account->id}");
            $client = $this->getEwsClient($account);

            // Get all folders for this account
            $folders = $this->getEwsFolders($account);

            $totalNewMessages = 0;
            $totalProcessedMessages = 0;
            $folderResults = [];

            // Get existing message IDs for this account
            $existingMessageIds = Email::where('account_id', $account->id)
                ->whereNotNull('message_id')
                ->pluck('message_id')
                ->toArray();

            Log::info("EWS: Found " . count($existingMessageIds) . " existing message IDs for account {$account->id}");

            // Determine the date filter for new messages
            $lastChecked = $account->last_checked_at;
            $dateRestriction = null;
            if ($lastChecked) {
                // Create date restriction for messages since last check
                $dateRestriction = new RestrictionType();
                $dateRestriction->IsGreaterThan = new IsGreaterThanType();
                $dateRestriction->IsGreaterThan->FieldURI = new PathToUnindexedFieldType();
                $dateRestriction->IsGreaterThan->FieldURI->FieldURI = UnindexedFieldURIType::ITEM_DATE_TIME_RECEIVED;
                $dateRestriction->IsGreaterThan->FieldURIOrConstant = new FieldURIOrConstantType();
                $dateRestriction->IsGreaterThan->FieldURIOrConstant->Constant = new ConstantValueType();
                $dateRestriction->IsGreaterThan->FieldURIOrConstant->Constant->Value = $lastChecked->toIso8601String();

                Log::info("EWS: Using date filter for account {$account->id}: messages since {$lastChecked->toIso8601String()}");
            }

            foreach ($folders as $folder) {
                try {
                    $folderName = $folder['name'];
                    $folderId = $folder['id'];

                    Log::info("EWS: Checking folder '{$folderName}' for account {$account->id}");

                    // Create the request to find items
                    $request = new FindItemType();

                    // Set the folder to search in
                    $request->ParentFolderIds = new NonEmptyArrayOfBaseFolderIdsType();

                    // Check if this is a distinguished folder
                    $distinguishedFolders = [
                        DistinguishedFolderIdNameType::INBOX,
                        DistinguishedFolderIdNameType::SENT,
                        DistinguishedFolderIdNameType::DRAFTS,
                        DistinguishedFolderIdNameType::DELETED,
                        DistinguishedFolderIdNameType::JUNK,
                    ];

                    if (in_array($folderId, $distinguishedFolders)) {
                        // Use distinguished folder ID for standard folders
                        $folderIdObj = new DistinguishedFolderIdType();
                        $folderIdObj->Id = $folderId;
                        $request->ParentFolderIds->DistinguishedFolderId[] = $folderIdObj;
                    } else {
                        // Use regular folder ID for custom folders
                        $folderIdObj = new FolderIdType();
                        $folderIdObj->Id = $folderId;
                        $folderIdObj->ChangeKey = $folder['changeKey'];
                        $request->ParentFolderIds->FolderId[] = $folderIdObj;
                    }

                    // Add date restriction if we have a last checked date
                    if ($dateRestriction) {
                        $request->Restriction = $dateRestriction;
                    }

                    // Set the item shape
                    $itemShape = new ItemResponseShapeType();
                    $itemShape->BaseShape = DefaultShapeNamesType::ID_ONLY;
                    $request->ItemShape = $itemShape;

                    Log::info("EWS: Sending FindItem request for folder '{$folderName}' in account {$account->id}");
                    // Send the request
                    $response = $client->FindItem($request);

                    if ($response->ResponseMessages->FindItemResponseMessage[0]->ResponseClass !== ResponseClassType::SUCCESS) {
                        throw new Exception($response->ResponseMessages->FindItemResponseMessage[0]->MessageText);
                    }

                    $items = $response->ResponseMessages->FindItemResponseMessage[0]->RootFolder->Items->Message ?? [];
                    $folderTotalMessages = count($items);

                    Log::info("EWS: Found {$folderTotalMessages} messages in folder '{$folderName}' for account {$account->id}");

                    $folderNewMessages = 0;
                    $processedMessageIds = []; // Keep track of message IDs we've already processed

                    foreach ($items as $item) {
                        try {
                            // Get the full item
                            $getItemRequest = new GetItemType();
                            $getItemRequest->ItemIds = new NonEmptyArrayOfBaseFolderIdsType();
                            $getItemRequest->ItemIds->ItemId[] = $item->ItemId;

                            // Set the item shape to get all properties
                            $itemShape = new ItemResponseShapeType();
                            $itemShape->BaseShape = DefaultShapeNamesType::ALL_PROPERTIES;
                            $getItemRequest->ItemShape = $itemShape;

                            Log::info("EWS: Getting full message details for item in folder '{$folderName}' for account {$account->id}");
                            $getItemResponse = $client->GetItem($getItemRequest);

                            if ($getItemResponse->ResponseMessages->GetItemResponseMessage[0]->ResponseClass !== ResponseClassType::SUCCESS) {
                                Log::warning("EWS: Failed to get item details in folder '{$folderName}' for account {$account->id}");
                                continue;
                            }

                            $message = $getItemResponse->ResponseMessages->GetItemResponseMessage[0]->Items->Message[0];

                            // Get the message ID
                            $messageId = $message->InternetMessageId ?? null;

                            // Skip if we've already processed this message ID in this session
                            if ($messageId && in_array($messageId, $processedMessageIds)) {
                                Log::info("EWS: Skipping message (already processed in this session) in folder '{$folderName}' for account {$account->id}");
                                continue;
                            }

                            // Add to processed message IDs
                            if ($messageId) {
                                $processedMessageIds[] = $messageId;
                            }

                            // Skip if we already have this message ID in the database
                            if ($messageId && in_array($messageId, $existingMessageIds)) {
                                Log::info("EWS: Skipping message (already exists in database) in folder '{$folderName}' for account {$account->id}");
                                continue;
                            }

                            Log::info("EWS: Saving new message with subject '{$message->Subject}' in folder '{$folderName}' for account {$account->id}");

                            // Check if this message ID already exists but doesn't have an eml_path
                            $existingEmail = null;
                            if ($messageId) {
                                $existingEmail = Email::where('account_id', $account->id)
                                    ->where('message_id', $messageId)
                                    ->first();
                            }

                            // If the email exists and already has an eml_path, skip saving a new .eml file
                            $emlPath = null;
                            if (!$existingEmail || !$existingEmail->eml_path) {
                                // Save the email as an .eml file in S3 storage
                                $emlPath = $this->saveEmailAsEml($account, $message, 'EWS');
                            } else {
                                $emlPath = $existingEmail->eml_path;
                                Log::info("EWS: Using existing .eml file: {$emlPath}");
                            }

                            if (!$existingEmail) {
                                // Create a new email record
                                Email::create([
                                    'account_id' => $account->id,
                                    'message_id' => $messageId,
                                    'subject' => $message->Subject ?? '(No Subject)',
                                    'body_text' => $message->Body->_ ?? null,
                                    'from_email' => $message->From->Mailbox->EmailAddress ?? '',
                                    'from_name' => $message->From->Mailbox->Name ?? null,
                                    'to' => $this->formatEwsAddresses($message->ToRecipients->Mailbox ?? []),
                                    'cc' => $this->formatEwsAddresses($message->CcRecipients->Mailbox ?? []),
                                    'bcc' => $this->formatEwsAddresses($message->BccRecipients->Mailbox ?? []),
                                    'attachments' => $this->formatEwsAttachments($message->Attachments->FileAttachment ?? []),
                                    'date' => $message->DateTimeReceived ? Carbon::parse($message->DateTimeReceived) : now(),
                                    'is_read' => $message->IsRead ?? false,
                                    'folder' => $folderName,
                                    'eml_path' => $emlPath,
                                ]);
                                Log::info("EWS: Created new email record for message in folder '{$folderName}'");
                            }

                            $folderNewMessages++;
                            $totalNewMessages++;
                            Log::info("EWS: Successfully saved message in folder '{$folderName}' for account {$account->id}");
                        } catch (Exception $e) {
                            // Log the error but continue processing other messages
                            Log::warning("EWS: Error processing message in folder '{$folderName}': " . $e->getMessage(), [
                                'account_id' => $account->id,
                                'folder' => $folderName,
                                'exception' => $e,
                            ]);
                        }
                    }

                    $totalProcessedMessages += $folderTotalMessages;
                    $folderResults[$folderName] = [
                        'total_messages' => $folderTotalMessages,
                        'new_messages' => $folderNewMessages,
                    ];

                    Log::info("EWS: Completed checking folder '{$folderName}'. Found {$folderNewMessages} new messages out of {$folderTotalMessages} total for account {$account->id}");

                } catch (Exception $e) {
                    Log::error("EWS: Error checking folder '{$folderName}' for account {$account->id}: " . $e->getMessage(), [
                        'account_id' => $account->id,
                        'folder' => $folderName,
                        'exception' => $e,
                    ]);
                    $folderResults[$folderName] = [
                        'total_messages' => 0,
                        'new_messages' => 0,
                        'error' => $e->getMessage(),
                    ];
                }
            }

            Log::info("EWS: Completed checking all folders. Found {$totalNewMessages} new messages out of {$totalProcessedMessages} total for account {$account->id}");

            return [
                'success' => true,
                'message' => "Found {$totalNewMessages} new messages out of {$totalProcessedMessages} total across " . count($folders) . " folders",
                'new_messages' => $totalNewMessages,
                'folder_results' => $folderResults,
            ];
        } catch (Exception $e) {
            Log::error("EWS: Error checking messages: " . $e->getMessage(), [
                'account_id' => $account->id,
                'exception' => $e,
            ]);
            throw $e;
        }
    }

    /**
     * Format email addresses for storage.
     *
     * @param array $addresses
     * @return array
     */
    protected function formatAddresses($addresses)
    {
        Log::info("formatAddresses : " . print_r($addresses, true));
        if (empty($addresses)) {
            return [];
        }

        $formatted = [];

        foreach ($addresses as $email => $name) {
            $formatted[] = [
                'email' => $email ?? '',
                'name' => $name ?? '',
            ];
        }

        return $formatted;
    }

    /**
     * Format attachments for storage.
     *
     * @param array $attachments
     * @return array
     */
    protected function formatAttachments($attachments)
    {
        if (empty($attachments)) {
            return [];
        }

        $formatted = [];

        foreach ($attachments as $id => $attachment) {
            // Get file size safely - check if property exists or use filesize function on filePath if available
            $size = 0;
            if (isset($attachment->filesize)) {
                $size = $attachment->filesize;
            } elseif (isset($attachment->filePath) && file_exists($attachment->filePath)) {
                $size = filesize($attachment->filePath);
            }

            $formatted[] = [
                'id' => $id,
                'name' => $attachment->name,
                'filename' => $attachment->filePath,
                'size' => $size,
                'mime' => $attachment->mime,
            ];
        }

        return $formatted;
    }

    /**
     * Save an email as an .eml file in S3 storage.
     *
     * @param Account $account
     * @param mixed $mail
     * @param string $protocol
     * @return string|null The path to the saved .eml file
     */
    protected function saveEmailAsEml($account, $mail, $protocol)
    {
        try {
            // Generate a unique filename based on account, date, and a random string
            $date = now()->format('Y-m-d-H-i-s');
            $random = substr(md5(rand()), 0, 10);
            $filename = "emails/{$account->id}/{$date}_{$random}.eml";

            Log::info("{$protocol}: Saving email as .eml file to S3: {$filename}");

            // Generate the .eml content based on the protocol
            $emlContent = $this->generateEmlContent($mail, $protocol, $account);

            if (!$emlContent) {
                Log::warning("{$protocol}: Failed to generate .eml content for account {$account->id}");
                return null;
            }

            // Save the .eml file to S3
            Storage::disk('s3')->put($filename, $emlContent);

            Log::info("{$protocol}: Successfully saved .eml file to S3: {$filename}");

            return $filename;
        } catch (Exception $e) {
            Log::error("{$protocol}: Error saving .eml file: " . $e->getMessage(), [
                'account_id' => $account->id,
                'exception' => $e,
            ]);

            return null;
        }
    }

    /**
     * Generate .eml content for an email.
     *
     * @param mixed $mail
     * @param string $protocol
     * @param Account $account
     * @return string|null
     */
    protected function generateEmlContent($mail, $protocol, ?Account $account = null)
    {
        try {
            switch ($protocol) {
                case 'IMAP':
                case 'POP3':
                    // For IMAP and POP3, we can use the raw header and body
                    $headers = $mail->headersRaw ?? '';
                    $body = $mail->textPlain ?? $mail->textHtml ?? '';

                    // If we have attachments, we need to create a MIME message
                    if (!empty($mail->getAttachments())) {
                        $boundary = md5(time());

                        // Set content type to multipart/mixed with boundary
                        if (!preg_match('/^Content-Type:/im', $headers)) {
                            $headers .= "Content-Type: multipart/mixed; boundary=\"{$boundary}\"\r\n";
                        } else {
                            // Replace existing Content-Type header
                            $headers = preg_replace(
                                '/^Content-Type:.*$/im',
                                "Content-Type: multipart/mixed; boundary=\"{$boundary}\"",
                                $headers
                            );
                        }

                        // Start the message body
                        $content = "--{$boundary}\r\n";

                        // Determine content type for the body
                        $bodyContentType = "text/plain";
                        if ($mail->textHtml) {
                            $bodyContentType = "text/html";
                        }

                        $content .= "Content-Type: {$bodyContentType}; charset=UTF-8\r\n";
                        $content .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
                        $content .= $body . "\r\n\r\n";

                        // Add attachments
                        foreach ($mail->getAttachments() as $attachment) {
                            $content .= "--{$boundary}\r\n";
                            $content .= "Content-Type: " . ($attachment->mime ?? 'application/octet-stream') . "\r\n";
                            $content .= "Content-Disposition: attachment; filename=\"" . ($attachment->name ?? 'attachment') . "\"\r\n";
                            $content .= "Content-Transfer-Encoding: base64\r\n\r\n";

                            // Read the attachment file and base64 encode it
                            if (file_exists($attachment->filePath)) {
                                $fileContent = file_get_contents($attachment->filePath);
                                $content .= chunk_split(base64_encode($fileContent)) . "\r\n";
                                unlink($attachment->filePath);
                            } else {
                                $content .= "[Attachment file not found: {$attachment->filePath}]\r\n\r\n";
                            }
                        }

                        // End the MIME message
                        $content .= "--{$boundary}--\r\n";

                        return $headers . "\r\n" . $content;
                    }

                    return $headers . "\r\n\r\n" . $body;

                case 'EWS':
                    // For EWS, we need to construct the .eml content from the available fields
                    $headers = "From: " . ($mail->From->Mailbox->Name ? "\"" . $mail->From->Mailbox->Name . "\" " : "") . "<" . ($mail->From->Mailbox->EmailAddress ?? '<EMAIL>') . ">\r\n";
                    $headers .= "To: " . $this->formatEwsAddressesForEml($mail->ToRecipients->Mailbox ?? []) . "\r\n";

                    if (!empty($mail->CcRecipients->Mailbox)) {
                        $headers .= "Cc: " . $this->formatEwsAddressesForEml($mail->CcRecipients->Mailbox) . "\r\n";
                    }

                    if (!empty($mail->BccRecipients->Mailbox)) {
                        $headers .= "Bcc: " . $this->formatEwsAddressesForEml($mail->BccRecipients->Mailbox) . "\r\n";
                    }

                    $headers .= "Subject: " . ($mail->Subject ?? '(No Subject)') . "\r\n";
                    $headers .= "Date: " . ($mail->DateTimeReceived ?? now()->toRfc2822String()) . "\r\n";
                    $headers .= "Message-ID: " . ($mail->InternetMessageId ?? '<' . md5(rand()) . '@example.com>') . "\r\n";

                    // Determine content type
                    $contentType = "text/plain";
                    if ($mail->Body && isset($mail->Body->BodyType) && $mail->Body->BodyType === 'HTML') {
                        $contentType = "text/html";
                    }

                    // Check if there are attachments
                    if (!empty($mail->Attachments->FileAttachment)) {
                        $boundary = md5(time());
                        $headers .= "Content-Type: multipart/mixed; boundary=\"{$boundary}\"\r\n";

                        // Start the message body
                        $content = "--{$boundary}\r\n";
                        $content .= "Content-Type: {$contentType}; charset=UTF-8\r\n";
                        $content .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
                        $content .= ($mail->Body->_ ?? '') . "\r\n\r\n";

                        // Add attachments
                        foreach ($mail->Attachments->FileAttachment as $attachment) {
                            $content .= "--{$boundary}\r\n";
                            $content .= "Content-Type: " . ($attachment->ContentType ?? 'application/octet-stream') . "\r\n";
                            $content .= "Content-Disposition: attachment; filename=\"" . ($attachment->Name ?? 'attachment') . "\"\r\n";
                            $content .= "Content-Transfer-Encoding: base64\r\n\r\n";

                            // Add the attachment content
                            if (isset($attachment->Content)) {
                                // The Content property in EWS is already base64 encoded
                                $content .= chunk_split($attachment->Content) . "\r\n";
                            } else if (isset($attachment->AttachmentId->Id)) {
                                // If we don't have the content but have an attachment ID, try to get it
                                try {
                                    // Log that we're trying to get the attachment content
                                    Log::info("EWS: Trying to get attachment content for {$attachment->Name}");

                                    // Get the EWS client for the account
                                    $client = $this->getEwsClient($account);

                                    // Get the attachment content
                                    $attachmentContent = $this->getEwsAttachmentContent($client, $attachment->AttachmentId->Id);

                                    if ($attachmentContent) {
                                        // The content is already base64 encoded
                                        $content .= chunk_split(base64_encode($attachmentContent)) . "\r\n";
                                    } else {
                                        $content .= "[Attachment content not available]\r\n\r\n";
                                    }
                                } catch (Exception $e) {
                                    Log::error("EWS: Error getting attachment content: " . $e->getMessage());
                                    $content .= "[Error getting attachment content]\r\n\r\n";
                                }
                            } else {
                                $content .= "[Attachment content not available - no attachment ID]\r\n\r\n";
                            }
                        }

                        // End the MIME message
                        $content .= "--{$boundary}--\r\n";

                        return $headers . "\r\n" . $content;
                    } else {
                        $headers .= "Content-Type: {$contentType}; charset=UTF-8\r\n";
                        $body = $mail->Body->_ ?? '';
                        return $headers . "\r\n\r\n" . $body;
                    }

                default:
                    Log::warning("Unknown protocol: {$protocol}");
                    return null;
            }
        } catch (Exception $e) {
            Log::error("Error generating .eml content: " . $e->getMessage(), [
                'protocol' => $protocol,
                'exception' => $e,
            ]);

            return null;
        }
    }

    /**
     * Format EWS attachments for storage.
     *
     * @param array $attachments
     * @return array
     */
    protected function formatEwsAttachments($attachments)
    {
        if (empty($attachments)) {
            return [];
        }

        $formatted = [];

        foreach ($attachments as $attachment) {
            $formatted[] = [
                'id' => $attachment->AttachmentId->Id ?? md5(rand()),
                'name' => $attachment->Name ?? 'attachment',
                'filename' => null, // EWS attachments don't have a local file path
                'size' => $attachment->Size ?? 0,
                'mime' => $attachment->ContentType ?? 'application/octet-stream',
            ];
        }

        return $formatted;
    }

    /**
     * Get attachment content from EWS.
     *
     * @param EwsClient $client
     * @param string $attachmentId
     * @return string|null
     */
    protected function getEwsAttachmentContent(EwsClient $client, $attachmentId)
    {
        try {
            Log::info("EWS: Getting attachment content for ID: {$attachmentId}");

            // Create the GetAttachment request
            $request = new \jamesiarmes\PhpEws\Request\GetAttachmentType();

            // Set the attachment IDs
            $request->AttachmentIds = new \jamesiarmes\PhpEws\ArrayType\NonEmptyArrayOfRequestAttachmentIdsType();
            $attachmentIdType = new \jamesiarmes\PhpEws\Type\RequestAttachmentIdType();
            $attachmentIdType->Id = $attachmentId;
            $request->AttachmentIds->AttachmentId[] = $attachmentIdType;

            // Send the request
            $response = $client->GetAttachment($request);

            // Check if the request was successful
            if ($response->ResponseMessages->GetAttachmentResponseMessage[0]->ResponseClass !== ResponseClassType::SUCCESS) {
                throw new Exception($response->ResponseMessages->GetAttachmentResponseMessage[0]->MessageText);
            }

            // Get the attachment
            $attachment = $response->ResponseMessages->GetAttachmentResponseMessage[0]->Attachments->FileAttachment[0];

            // Return the content
            return $attachment->Content ?? null;
        } catch (Exception $e) {
            Log::error("EWS: Error getting attachment content: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Format EWS email addresses for storage.
     *
     * @param array $mailboxes
     * @return array
     */
    protected function formatEwsAddresses($mailboxes)
    {
        if (empty($mailboxes)) {
            return [];
        }

        $formatted = [];

        foreach ($mailboxes as $mailbox) {
            $formatted[] = [
                'email' => $mailbox->EmailAddress ?? '',
                'name' => $mailbox->Name ?? '',
            ];
        }

        return $formatted;
    }

    /**
     * Format EWS addresses for .eml format.
     *
     * @param array $mailboxes
     * @return string
     */
    protected function formatEwsAddressesForEml($mailboxes)
    {
        if (empty($mailboxes)) {
            return '';
        }

        $formatted = [];

        foreach ($mailboxes as $mailbox) {
            if ($mailbox->Name) {
                $formatted[] = "\"" . $mailbox->Name . "\" <" . ($mailbox->EmailAddress ?? '<EMAIL>') . ">";
            } else {
                $formatted[] = $mailbox->EmailAddress ?? '<EMAIL>';
            }
        }

        return implode(', ', $formatted);
    }
}
