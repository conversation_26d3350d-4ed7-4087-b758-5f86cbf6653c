# Security Policies Implementation

This document outlines the security policies implemented to protect account and email access in the MailBackupV2 application.

## Overview

The security implementation ensures that users can only access their own accounts and emails by checking that the `owner_id` of accounts matches the authenticated user's ID from `NarAuth::id()`.

## Components

### 1. Policies

#### AccountPolicy (`app/Policies/AccountPolicy.php`)
- **Purpose**: Controls access to Account model operations
- **Key Methods**:
  - `viewAny()`: Allows authenticated users to view their accounts list
  - `view()`: Checks if account belongs to authenticated user
  - `create()`: Allows authenticated users to create accounts
  - `update()`: Ensures only account owner can update
  - `delete()`: Ensures only account owner can delete
  - `test()`: Ensures only account owner can test connection
  - `check()`: Ensures only account owner can check for new messages

#### EmailPolicy (`app/Policies/EmailPolicy.php`)
- **Purpose**: Controls access to Email model operations
- **Key Methods**:
  - `viewAny()`: Checks account ownership before listing emails
  - `view()`: Ensures email belongs to user's account
  - `delete()`: Ensures only owner can delete emails
  - `viewRaw()`: Ensures only owner can view raw email content

### 2. Controllers

#### AccountsController (`app/Http/Controllers/Api/V1/AccountsController.php`)
- **Security Features**:
  - Uses `AuthorizesRequests` trait
  - Filters accounts by `owner_id` in index method
  - Automatically sets `owner_id` when creating accounts
  - Authorizes all operations using policies
  - Ensures users only see their own accounts

#### MailsController (`app/Http/Controllers/Api/V1/MailsController.php`)
- **Security Features**:
  - Uses `AuthorizesRequests` trait
  - Verifies account ownership before accessing emails
  - Authorizes all email operations using policies
  - Ensures emails belong to user's accounts

### 3. Middleware

#### EnsureAccountOwnership (`app/Http/Middleware/EnsureAccountOwnership.php`)
- **Purpose**: Additional layer of security for route model binding
- **Functionality**:
  - Checks if user is authenticated via NarAuth
  - Verifies account ownership when account is in route parameters
  - Returns 403 Forbidden if account doesn't belong to user

### 4. Model Updates

#### Account Model (`app/Models/Account.php`)
- **Changes**:
  - Added `owner_id` to fillable attributes
  - Added `belongsToUser()` helper method for ownership checks

### 5. Route Protection

#### API Routes (`routes/api.php`)
- **Structure**:
  - Account CRUD operations use policy authorization
  - Account-specific operations (test, check) use `account.owner` middleware
  - All mail operations require account ownership verification

## Security Flow

### Account Operations
1. User makes request to account endpoint
2. Controller authorizes action using AccountPolicy
3. Policy checks `NarAuth::id()` against `account.owner_id`
4. Operation proceeds only if ownership is verified

### Email Operations
1. User makes request to email endpoint with account parameter
2. `EnsureAccountOwnership` middleware verifies account ownership
3. Controller authorizes action using EmailPolicy
4. Policy checks account ownership through email->account relationship
5. Operation proceeds only if all checks pass

### Creating Accounts
1. User submits account creation request
2. Controller authorizes creation using AccountPolicy
3. Controller automatically sets `owner_id` to `NarAuth::id()`
4. Account is created with proper ownership

## Key Security Features

1. **Double Protection**: Both policies and middleware protect resources
2. **Automatic Ownership**: `owner_id` is set automatically, users cannot manipulate it
3. **Comprehensive Coverage**: All CRUD operations are protected
4. **Relationship Security**: Email access is protected through account ownership
5. **Authentication Integration**: Uses NarAuth for consistent user identification
6. **Laravel Policy Compatibility**: Policies properly handle Laravel's user parameter injection

## Important Notes

### Policy Method Signatures
All policy methods include a `$user` parameter as the first argument to comply with Laravel's authorization system. Laravel automatically injects the authenticated user object as the first parameter to policy methods. The actual authorization logic uses `NarAuth::id()` for consistency with the NarAuth authentication system.

Example:
```php
public function update($user, Account $account): bool
{
    return NarAuth::check() && $account->owner_id === NarAuth::id();
}
```

## Usage Examples

### Accessing Accounts
```php
// Only returns accounts where owner_id = NarAuth::id()
GET /api/v1/accounts

// Only shows account if owner_id = NarAuth::id()
GET /api/v1/accounts/1
```

### Accessing Emails
```php
// Only works if account belongs to authenticated user
GET /api/v1/mails/1

// Only shows email if it belongs to user's account
GET /api/v1/mails/1/5
```

### Creating Accounts
```php
// owner_id is automatically set to NarAuth::id()
POST /api/v1/accounts
{
    "name": "My Email Account",
    "email": "<EMAIL>",
    // ... other fields
}
```

## Error Responses

- **401 Unauthorized**: User not authenticated via NarAuth
- **403 Forbidden**: User trying to access resource they don't own
- **404 Not Found**: Resource doesn't exist or doesn't belong to user

This implementation ensures complete data isolation between users while maintaining a clean and secure API structure.
